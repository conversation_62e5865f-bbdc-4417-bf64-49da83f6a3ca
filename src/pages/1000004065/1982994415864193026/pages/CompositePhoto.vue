<template>
  <div class="composite-photo" v-if="!isShowShare">
    <div class="add-icon" v-if="!aiPicture">
      <input class="upload-input" type="file" accept="image/jpeg, image/png" @change="beforeRead" />
      <VanIcon size="50" name="add" />
    </div>
    <VanIcon @click="!aiPicture" v-if="aiPicture" class="remove-icon" size="25" name="//img10.360buyimg.com/imgzone/jfs/t1/345756/13/22951/1340/690b15a6F27e2cb72/db7a83140918a3a0.png" />
    <div class="composite">
      <!-- 图片操作组件 -->
      <ImageOperation class="composite-img" :backgroundImage="backgroundImage" :foregroundImage="aiPicture" :containerWidth="538" :containerHeight="774" :cropWidth="200" :cropHeight="400" :cropX="100" :cropY="100" @export="handleExport" />
      <!-- <img class="composite-img" src="//img10.360buyimg.com/imgzone/jfs/t1/337569/15/22432/135463/690b1302F4ff0a96f/8061ff8c6340ec53.png" alt="" srcset="" /> -->
      <!-- <div class="move-img-container" v-if="aiPicture">
        <img class="move-img" :src="aiPicture" alt="" srcset="" />
      </div> -->
    </div>
    <!-- 点击合成 -->
    <div class="composite-button" @click="handleComposite"></div>
  </div>
  <div class="share" v-if="isShowShare">
    <div class="share-photo">
      <img class="share-img" :src="compositeImage" alt="" />
      <img class="tips-img" src="//img10.360buyimg.com/imgzone/jfs/t1/356909/25/4569/23627/690c0672F712b0bb7/e039399db087f59b.png" alt="" />
      <img class="qrcode-img" :src="qrcodeImg" alt="" />
    </div>
    <div class="btn-group">
      <!-- 分享朋友圈 -->
      <div @click="shareToFriends"></div>
      <!-- 分享小红书 -->
      <div @click="shareToXiaoHongShu"></div>
    </div>
  </div>

  <PhotoLoading :isShowPopup="isLoading" />
  <ConfirmPicture @userConfirm="handleConfirm" :imgUrl="aiPicture" :isShowPopup="isShowConfirmDialog" @closeDialog="isShowConfirmDialog = false" />
</template>
<script lang="ts" setup>
import { showToast } from 'vant';
import { segmentBody, uploadImg } from '../script/ajax';
import PhotoLoading from '../components/PhotoLoading.vue';
import { ref } from 'vue';
import { compressImage } from '@/utils/ImageUtils';
import ConfirmPicture from '../components/ConfirmPicture.vue';
import ImageOperation from '../components/ImageOperation.vue';
import QRCode from 'qrcode';
import { callShare } from '@/utils/platforms/share';
import { inject } from 'vue';

const backgroundImage = '//img10.360buyimg.com/imgzone/jfs/t1/337569/15/22432/135463/690b1302F4ff0a96f/8061ff8c6340ec53.png';
const decoData = inject('decoData') as any;
const { XHFShareContent } = decoData;
const qrcodeImg = ref('');
QRCode.toDataURL(window.location.href)
  .then((url: string) => {
    qrcodeImg.value = url;
  })
  .catch((err: any) => {
    console.error(err);
  });

const isShowShare = ref(false);
const showImageOperation = ref(false);
const isLoading = ref(false);
const isShowConfirmDialog = ref(false);
const aiPicture = ref('//img10.360buyimg.com/imgzone/jfs/t1/353139/2/7340/42357/690c03eeF85c01d20/15f1867f4a5efa75.png');
const compositeImage = ref('');

const beforeRead = async (e: Event) => {
  isLoading.value = true;
  try {
    const target = e.target as HTMLInputElement;
    const files = target.files;
    const file = files && files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      showToast('请选择图片');
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      showToast('请上传小于5M的图片~');
      return false;
    }
    const compressedFile = await compressImage(file, { quality: 0.5 });
    let formData = new FormData();
    formData.append('file', compressedFile);
    // 上传图片到图片空间
    const result = await uploadImg(formData);
    isLoading.value = false;
    if (!result) return;
    // 抠图
    isLoading.value = true;
    const data = await segmentBody(result);
    aiPicture.value = data;
    isShowConfirmDialog.value = true;
    isLoading.value = false;
  } catch (error) {
    isLoading.value = false;
    console.error('上传图片失败:', error);
  }
};

const handleConfirm = () => {
  isShowConfirmDialog.value = false;
};

const handleComposite = () => {
  // 显示图片操作组件
  showImageOperation.value = true;
};

const handleExport = (dataUrl: string) => {
  // 保存合成后的图片
  compositeImage.value = dataUrl;
  // 隐藏图片操作组件
  showImageOperation.value = false;
  // 显示分享页面
  isShowShare.value = true;
};

const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
const shareToFriends = () => {
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: compositeImage.value || shareConfig.shareImage,
    channel: 'Wxmoments',
    afterShare: () => {},
  });
};

const shareToXiaoHongShu = () => {
  callShare({
    title: '宋雨琦邀你参与蝴蝶结星光派对啦!',
    content: XHFShareContent,
    imageUrl: compositeImage.value || shareConfig.shareImage,
    channel: 'XHS',
    afterShare: () => {},
  });
};
</script>
<style lang="scss" scoped>
.composite-photo {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/342049/17/23459/89400/690b10ecF6fbd284f/a868737f53e601c5.png) no-repeat center center;
  background-size: 100% 100%;
  width: 7.5rem;
  height: 16.24rem;
  padding-top: 3.85rem;
  box-sizing: border-box;
  position: relative;
  .add-icon {
    position: absolute;
    top: 9.5rem;
    right: 1.7rem;
    z-index: 1;
    .upload-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      z-index: 1;
      background-color: #fac;
    }
  }
  .remove-icon {
    position: absolute;
    top: 3.9rem;
    left: 1.3rem;
    z-index: 1;
  }
  .composite {
    width: 5.38rem;
    height: 7.74rem;
    margin: 0 auto 0;
    position: relative;
    .composite-img {
      width: 5.38rem;
      height: 7.74rem;
    }
    .move-img-container {
      position: absolute;
      top: 3rem;
      left: 3rem;
      right: 0rem;
      bottom: 0rem;
      background-color: #fac;
      img {
        width: 50%;
        height: 50%;
      }
    }
  }
  .composite-button {
    width: 4rem;
    height: 2rem;
    position: absolute;
    top: 14rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }
}
.share {
  position: relative;
}
.share-photo {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/357012/30/4691/62066/690c1afbF1c639684/d056acadf925cac7.png) no-repeat center center;
  background-size: 100% 100%;
  width: 7.5rem;
  height: 16.24rem;
  padding-top: 3rem;
  box-sizing: border-box;
  .share-img {
    width: 5.38rem;
    margin: 0 auto;
  }
  .tips-img {
    width: 4.95rem;
    margin: -0.3rem auto 0;
  }
  .qrcode-img {
    position: absolute;
    width: 1.85rem;
    right: 1.1rem;
    top: 12.65rem;
    border-radius: 0.2rem;
  }
}
.btn-group {
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/356963/30/4618/116332/690c1afdFeaf5bd03/466d272732cc03a0.png) no-repeat center center;
  background-size: 100% 100%;
  width: 7.5rem;
  height: 1.72rem;
  position: absolute;
  bottom: 0rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  div {
    flex: 1;
    height: 100%;
  }
}
</style>
