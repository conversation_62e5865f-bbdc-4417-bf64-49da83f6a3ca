<template>
  <template v-if="isShowMain">
    <KV :rightsSkuId="actInfo.rightsSkuId" :appointmentSkuId="actInfo.appointmentSkuId"></KV>
    <ExclusiveSurprise></ExclusiveSurprise>
    <TreasureGifts @draw-success="handleDrawSuccess" :starValue="actInfo.userCurrentStarValue"></TreasureGifts>
    <WonderfulMission @doCompositePhoto="isShowMain = false" @doTaskSuccess="handleDoTaskSuccess"></WonderfulMission>
    <img class="w100" src="//img10.360buyimg.com/imgzone/jfs/t1/349905/25/20677/72461/69041de6Fcd546ff7/1bb18cc2f07689b6.png" alt="" />
    <div class="surprise-treat">
      <div @click="gotoAnyLink(item)" v-for="item in bannerList" :key="item"></div>
    </div>
    <SkuList></SkuList>
    <img class="logo" @click="gotoShopPage(baseInfo.shopId)" src="./assets/OPPO.png" alt="" />
    <UnlockScreen :isShowPopup="isShowScreenialog" @close-dialog="closedScreenDialog"></UnlockScreen>
  </template>
  <CompositePhoto v-else></CompositePhoto>
</template>
<script lang="ts" setup>
import KV from './pages/KV.vue';
import ExclusiveSurprise from './pages/ExclusiveSurprise.vue';
import TreasureGifts from './pages/TreasureGifts.vue';
import WonderfulMission from './pages/WonderfulMission.vue';
import SkuList from './pages/SkuList.vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { inject, ref, onMounted, reactive } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { showToast } from 'vant';
import UnlockScreen from './components/UnlockScreen.vue';
import { getActivityInfo, orderSku, rightOrderSku, shareUser } from './script/ajax';
import type { ActInfo } from './script/type';
import CompositePhoto from './pages/CompositePhoto.vue';

const isShowMain = ref(true);
const baseInfo = inject('baseInfo') as BaseInfo;
const pathParams = inject('pathParams') as any;
const decoData = inject('decoData') as any;
const { bannerList, checkOrderStartTime, checkOrderEndTime, checkYiYuanStartTime, checkYiYuanEndTime } = decoData;
const isShowScreenialog = ref(false);
const closedScreenDialog = () => {
  isShowScreenialog.value = false;
  if (!actInfo.appointmentSkuStatus) {
    showToast('预约成功~');
    const timer = setTimeout(() => {
      clearTimeout(timer);
    }, 2000);
    return;
  }
  checkShare();
};
const actInfo = reactive<ActInfo>({
  todayNewUser: false,
  appointmentSkuStatus: false,
  rightsSkuId: 0,
  appointmentSkuId: 0,
  userCurrentStarValue: 0,
});
// 注意  下单接口需要在指定时间内调用
const checkOrder = async () => {
  if (checkOrderStartTime && checkOrderEndTime) {
    if (new Date().getTime() >= checkOrderStartTime && new Date().getTime() <= checkOrderEndTime) {
      await orderSku();
    }
  }
};
// 注意  下单接口需要在指定时间内调用
const checkRightOrder = async () => {
  if (checkYiYuanStartTime && checkYiYuanEndTime) {
    if (new Date().getTime() >= checkYiYuanStartTime && new Date().getTime() <= checkYiYuanEndTime) {
      await rightOrderSku();
    }
  }
};
const checkShare = async () => {
  if (pathParams.shareId) {
    const data = await shareUser(pathParams.shareId);
    if (data) {
      showToast('助力成功~');
    }
  }
};
onMounted(async () => {
  const params = {
    onClickBtnBack: () => {
      console.log('拦截成功后用户的回调');
      if (!isShowMain.value) {
        isShowMain.value = true;
      } else {
        window.jmfe.closeWebview();
      }
    },
    controlType: '2',
  };
  window.jmfe.backControlRouter(params).then((res: any) => {
    console.log('🚀 ~ window.jmfe.backControlRouter ~ res:', res);
  });
  Promise.all([checkOrder(), checkRightOrder()]);
  const result = await getActivityInfo();
  if (!result) return;
  Object.assign(actInfo, result);

  if (actInfo.todayNewUser) {
    isShowScreenialog.value = true;
    window.localStorage.removeItem('browseSkuIds');
  }
  if (!actInfo.appointmentSkuStatus) {
    showToast('预约成功~');
    const timer = setTimeout(() => {
      clearTimeout(timer);
      checkShare();
    }, 2000);
  }
});
const handleDrawSuccess = async () => {
  console.log('🚀 ~ handleDrawSuccess');
  //   更新用户积分  抽奖-200
  //   const result = await getActivityInfo();
  //   if (!result) return;
  //   Object.assign(actInfo, result);
  actInfo.userCurrentStarValue -= 200;
};
const handleDoTaskSuccess = async () => {
  //  做完任务 更新用户积分
  const result = await getActivityInfo();
  if (!result) return;
  Object.assign(actInfo, result);
};
const gotoAnyLink = (link: string) => {
  link && (window.location.href = link);
};
</script>
<style lang="scss" scoped>
.surprise-treat {
  padding: 2.3rem 0.3rem 0;
  box-sizing: border-box;
  background: url(//img10.360buyimg.com/imgzone/jfs/t1/351326/28/22279/122254/690b15d4Fb6e3c668/b5feb24e78da4bc5.png) no-repeat;
  background-size: contain;
  width: 7.5rem;
  div {
    height: 2.52rem;
    margin-top: 0.1rem;
  }
}
.logo {
  width: 2.79rem;
  margin: 0.8rem auto;
}
</style>
<style>
.w100 {
  width: 100%;
}
.btn-disabled {
  filter: grayscale(1);
}
html {
  font-family: 'OPPOSansR';
  background: url(./assets/bg.png) no-repeat;
  background-size: 100%;
}
/*修改滚动条样式*/
div::-webkit-scrollbar {
  display: none;
}
@font-face {
  font-family: 'TsangerYuYangT';
  src: url('https://lzcdn.dianpusoft.cn/fonts/TsangerYuYangT/TsangerYuYangT-W04.ttf') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'TsangerYuYang03';
  src: url('https://lzcdn.dianpusoft.cn/fonts/TsangerYuYangT/TsangerYuYangT-W03.ttf') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'OPPOSansR';
  src: url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-R.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-R.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'OPPOSansH';
  src: url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-H.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-H.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'OPPOSansM';
  src: url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-M.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-M.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.dialog {
  font-family: 'TsangerYuYang03';
}
</style>
