<template>
  <div class="image-operation">
    <!-- Canvas 操作区域 -->
    <div class="canvas-container">
      <canvas ref="canvasRef" :width="containerWidth" :height="containerHeight" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseUp" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd" @wheel="handleWheel"></canvas>
    </div>

    <!-- 操作按钮 -->
    <div class="controls">
      <button @click="zoomIn" class="control-btn zoom-in">+</button>
      <button @click="zoomOut" class="control-btn zoom-out">-</button>
      <button @click="reset" class="control-btn reset">重置</button>
      <button @click="exportImage" class="control-btn export">导出</button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, watch } from 'vue';
import { showToast } from 'vant';

// 定义组件属性
interface Props {
  backgroundImage?: string; // 背景图片URL
  foregroundImage?: string; // 前景图片URL
  containerWidth?: number; // 容器宽度
  containerHeight?: number; // 容器高度
  cropWidth?: number; // 裁剪区域宽度
  cropHeight?: number; // 裁剪区域高度
  cropX?: number; // 裁剪区域X坐标（相对于容器）
  cropY?: number; // 裁剪区域Y坐标（相对于容器）
}

const props = withDefaults(defineProps<Props>(), {
  backgroundImage: '',
  foregroundImage: '',
  containerWidth: 300,
  containerHeight: 400,
  cropWidth: 200,
  cropHeight: 200,
  cropX: undefined, // 默认居中
  cropY: undefined, // 默认居中
});

// 定义事件发射
const emit = defineEmits<{
  (e: 'export', dataUrl: string): void;
}>();

// 响应式数据
const canvasRef = ref<HTMLCanvasElement | null>(null);
const backgroundImg = ref<HTMLImageElement | null>(null);
const foregroundImg = ref<HTMLImageElement | null>(null);
const isDragging = ref(false);
const startPos = ref({ x: 0, y: 0 });
const pinchStart = ref({
  distance: 0,
  scale: 1,
});
const imageState = ref({
  x: 0,
  y: 0,
  scale: 1,
});

// 初始化
onMounted(() => {
  loadImages();
});

// 监听图片变化
watch(
  () => [props.backgroundImage, props.foregroundImage],
  () => {
    loadImages();
  },
);

// 加载图片
const loadImages = () => {
  // 清除之前的图片引用
  backgroundImg.value = null;
  foregroundImg.value = null;

  if (props.backgroundImage) {
    backgroundImg.value = new Image();
    backgroundImg.value.crossOrigin = 'anonymous';
    backgroundImg.value.onload = () => {
      redrawCanvas();
    };
    backgroundImg.value.onerror = () => {
      showToast('背景图片加载失败');
    };
    backgroundImg.value.src = props.backgroundImage;
  }

  if (props.foregroundImage) {
    foregroundImg.value = new Image();
    foregroundImg.value.crossOrigin = 'anonymous';
    foregroundImg.value.onload = () => {
      reset();
      redrawCanvas();
    };
    foregroundImg.value.onerror = () => {
      showToast('前景图片加载失败');
    };
    foregroundImg.value.src = props.foregroundImage;
  }
};

// 重绘 Canvas
const redrawCanvas = () => {
  if (!canvasRef.value) return;

  const ctx = canvasRef.value.getContext('2d');
  if (!ctx) return;

  // 清空画布
  ctx.clearRect(0, 0, props.containerWidth, props.containerHeight);

  // 绘制背景图片
  if (backgroundImg.value && backgroundImg.value.complete) {
    ctx.drawImage(backgroundImg.value, 0, 0, props.containerWidth, props.containerHeight);
  }

  // 绘制前景图片
  if (foregroundImg.value && foregroundImg.value.complete) {
    const scaledWidth = foregroundImg.value.width * imageState.value.scale;
    const scaledHeight = foregroundImg.value.height * imageState.value.scale;

    ctx.drawImage(foregroundImg.value, imageState.value.x, imageState.value.y, scaledWidth, scaledHeight);
  }

  // 绘制裁剪框
  drawCropFrame(ctx);
};

// 计算裁剪区域的位置
const getCropPosition = () => {
  // 如果提供了自定义位置，则使用自定义位置
  if (props.cropX !== undefined && props.cropY !== undefined) {
    return {
      x: props.cropX,
      y: props.cropY,
    };
  }

  // 默认居中
  return {
    x: (props.containerWidth - props.cropWidth) / 2,
    y: (props.containerHeight - props.cropHeight) / 2,
  };
};

// 绘制裁剪框
const drawCropFrame = (ctx: CanvasRenderingContext2D) => {
  const cropPos = getCropPosition();

  // 只绘制裁剪框边框，不绘制遮罩
  ctx.strokeStyle = '#ff6b6b';
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);
  ctx.strokeRect(cropPos.x, cropPos.y, props.cropWidth, props.cropHeight);
  ctx.setLineDash([]);

  // 绘制裁剪框角标
  ctx.strokeStyle = '#ff6b6b';
  ctx.lineWidth = 3;
  ctx.lineCap = 'round';

  const cornerLength = 15;

  // 左上角
  ctx.beginPath();
  ctx.moveTo(cropPos.x, cropPos.y + cornerLength);
  ctx.lineTo(cropPos.x, cropPos.y);
  ctx.lineTo(cropPos.x + cornerLength, cropPos.y);
  ctx.stroke();

  // 右上角
  ctx.beginPath();
  ctx.moveTo(cropPos.x + props.cropWidth - cornerLength, cropPos.y);
  ctx.lineTo(cropPos.x + props.cropWidth, cropPos.y);
  ctx.lineTo(cropPos.x + props.cropWidth, cropPos.y + cornerLength);
  ctx.stroke();

  // 左下角
  ctx.beginPath();
  ctx.moveTo(cropPos.x, cropPos.y + props.cropHeight - cornerLength);
  ctx.lineTo(cropPos.x, cropPos.y + props.cropHeight);
  ctx.lineTo(cropPos.x + cornerLength, cropPos.y + props.cropHeight);
  ctx.stroke();

  // 右下角
  ctx.beginPath();
  ctx.moveTo(cropPos.x + props.cropWidth - cornerLength, cropPos.y + props.cropHeight);
  ctx.lineTo(cropPos.x + props.cropWidth, cropPos.y + props.cropHeight);
  ctx.lineTo(cropPos.x + props.cropWidth, cropPos.y + props.cropHeight - cornerLength);
  ctx.stroke();
};

// 鼠标滚轮缩放处理
const handleWheel = (e: WheelEvent) => {
  if (!foregroundImg.value) return;

  e.preventDefault();

  // 根据滚轮方向调整缩放比例
  const delta = e.deltaY > 0 ? 0.9 : 1.1;
  imageState.value.scale = Math.min(Math.max(imageState.value.scale * delta, 0.1), 3);

  // 限制位置范围
  limitPosition();

  redrawCanvas();
};

// 鼠标事件处理
const handleMouseDown = (e: MouseEvent) => {
  if (foregroundImg.value) {
    isDragging.value = true;
    startPos.value.x = e.clientX;
    startPos.value.y = e.clientY;
    e.preventDefault();
  }
};

const handleMouseMove = (e: MouseEvent) => {
  if (isDragging.value && foregroundImg.value) {
    const deltaX = e.clientX - startPos.value.x;
    const deltaY = e.clientY - startPos.value.y;

    imageState.value.x += deltaX;
    imageState.value.y += deltaY;

    startPos.value.x = e.clientX;
    startPos.value.y = e.clientY;

    // 限制移动范围
    limitPosition();

    redrawCanvas();
  }
};

const handleMouseUp = () => {
  isDragging.value = false;
};

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
  if (e.touches.length === 1 && foregroundImg.value) {
    // 单指触摸，移动图片
    isDragging.value = true;
    const touch = e.touches[0];
    startPos.value.x = touch.clientX;
    startPos.value.y = touch.clientY;
  } else if (e.touches.length === 2 && foregroundImg.value) {
    // 双指触摸，缩放图片
    isDragging.value = false; // 停止移动操作
    const touch1 = e.touches[0];
    const touch2 = e.touches[1];

    // 计算初始双指距离
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    pinchStart.value.distance = Math.sqrt(dx * dx + dy * dy);
    pinchStart.value.scale = imageState.value.scale;
  }
};

const handleTouchMove = (e: TouchEvent) => {
  if (isDragging.value && e.touches.length === 1 && foregroundImg.value) {
    // 单指移动图片
    const touch = e.touches[0];
    const deltaX = touch.clientX - startPos.value.x;
    const deltaY = touch.clientY - startPos.value.y;

    imageState.value.x += deltaX;
    imageState.value.y += deltaY;

    startPos.value.x = touch.clientX;
    startPos.value.y = touch.clientY;

    // 限制移动范围
    limitPosition();

    redrawCanvas();
    e.preventDefault();
  } else if (e.touches.length === 2 && foregroundImg.value) {
    // 双指缩放图片
    const touch1 = e.touches[0];
    const touch2 = e.touches[1];

    // 计算当前双指距离
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 计算缩放比例
    const scale = pinchStart.value.scale * (distance / pinchStart.value.distance);

    // 限制缩放范围
    imageState.value.scale = Math.min(Math.max(scale, 0.1), 3);

    // 限制位置范围
    limitPosition();

    redrawCanvas();
    e.preventDefault();
  }
};

const handleTouchEnd = () => {
  isDragging.value = false;
};

// 限制图片位置在裁剪区域内
const limitPosition = () => {
  if (!foregroundImg.value || !canvasRef.value) return;

  const scaledWidth = foregroundImg.value.width * imageState.value.scale;
  const scaledHeight = foregroundImg.value.height * imageState.value.scale;
  const cropPos = getCropPosition();

  // 计算裁剪区域的边界
  const cropRight = cropPos.x + props.cropWidth;
  const cropBottom = cropPos.y + props.cropHeight;

  // 限制X轴位置
  if (scaledWidth <= props.cropWidth) {
    // 图片宽度小于等于裁剪区域宽度，居中显示
    imageState.value.x = cropPos.x + (props.cropWidth - scaledWidth) / 2;
  } else {
    // 图片宽度大于裁剪区域宽度，限制在裁剪区域内
    if (imageState.value.x > cropPos.x) {
      imageState.value.x = cropPos.x;
    }
    if (imageState.value.x + scaledWidth < cropRight) {
      imageState.value.x = cropRight - scaledWidth;
    }
  }

  // 限制Y轴位置
  if (scaledHeight <= props.cropHeight) {
    // 图片高度小于等于裁剪区域高度，居中显示
    imageState.value.y = cropPos.y + (props.cropHeight - scaledHeight) / 2;
  } else {
    // 图片高度大于裁剪区域高度，限制在裁剪区域内
    if (imageState.value.y > cropPos.y) {
      imageState.value.y = cropPos.y;
    }
    if (imageState.value.y + scaledHeight < cropBottom) {
      imageState.value.y = cropBottom - scaledHeight;
    }
  }
};

// 重置
const reset = () => {
  if (!foregroundImg.value) {
    imageState.value.x = 0;
    imageState.value.y = 0;
    imageState.value.scale = 1;
    redrawCanvas();
    return;
  }

  // 计算裁剪区域的中心位置
  const cropPos = getCropPosition();

  // 确保图片至少填满裁剪区域
  const scaleX = props.cropWidth / foregroundImg.value.width;
  const scaleY = props.cropHeight / foregroundImg.value.height;
  const scale = Math.max(scaleX, scaleY); // 使用最大比例确保图片填满裁剪区域

  imageState.value.scale = scale;

  // 居中显示在裁剪区域内
  const scaledWidth = foregroundImg.value.width * scale;
  const scaledHeight = foregroundImg.value.height * scale;

  imageState.value.x = cropPos.x + (props.cropWidth - scaledWidth) / 2;
  imageState.value.y = cropPos.y + (props.cropHeight - scaledHeight) / 2;

  redrawCanvas();
};

// 放大
const zoomIn = () => {
  imageState.value.scale = Math.min(imageState.value.scale * 1.1, 3);
  limitPosition();
  redrawCanvas();
};

// 缩小
const zoomOut = () => {
  imageState.value.scale = Math.max(imageState.value.scale / 1.1, 0.1);
  limitPosition();
  redrawCanvas();
};

// 导出图片
const exportImage = () => {
  if (!canvasRef.value) {
    showToast('无法导出图片');
    return;
  }

  try {
    // 计算裁剪区域在原 canvas 中的位置
    const cropPos = getCropPosition();

    // 创建一个新的 canvas 用于导出，只包含裁剪区域的内容
    const exportCanvas = document.createElement('canvas');
    exportCanvas.width = props.cropWidth;
    exportCanvas.height = props.cropHeight;

    const exportCtx = exportCanvas.getContext('2d');
    if (!exportCtx) {
      showToast('导出图片失败');
      return;
    }

    // 从原 canvas 中裁剪并绘制到导出 canvas
    exportCtx.drawImage(canvasRef.value, cropPos.x, cropPos.y, props.cropWidth, props.cropHeight, 0, 0, props.cropWidth, props.cropHeight);

    const dataUrl = exportCanvas.toDataURL('image/png');
    emit('export', dataUrl);
  } catch (error) {
    showToast('导出图片失败');
  }
};
</script>

<style lang="scss" scoped>
.image-operation {
  width: 100%;

  .canvas-container {
    // width: v-bind('props.containerWidth + "px"');
    // height: v-bind('props.containerHeight + "px"');
    width: 100%;
    height: 100%;
    margin: 0 auto;
    overflow: hidden;

    canvas {
      display: block;
      cursor: move;
    }
  }

  .controls {
    display: flex;
    justify-content: center;
    gap: 0.1333rem;
    margin-top: 0.2667rem;

    .control-btn {
      padding: 0.1067rem 0.2133rem;
      border: 0.0133rem solid #ddd;
      background-color: #fff;
      border-radius: 0.0533rem;
      cursor: pointer;
      font-size: 0.1867rem;

      &:hover {
        background-color: #f5f5f5;
      }

      &.export {
        background-color: #1989fa;
        color: white;
        border-color: #1989fa;

        &:hover {
          background-color: #0570db;
        }
      }
    }
  }
}
</style>
