<!--
* @Description: caoshijie
* @Date: 2025-10-31 11:50:24
* @Description: 词云
* @FilePath: src\pages\1000004065\1982994415864193026\components\WordCloud.vue 
-->
<template>
  <div class="word-cloud">
    <div ref="wordCloud" style="width: 100%; height: 100%"></div>
  </div>
</template>
<script lang="ts" setup>
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import { ref, onMounted, inject } from 'vue';
import word from '../assets/mission/path.png';
import { getSelectedMessageList } from '../script/ajax';

const decoData = inject('decoData') as any;
const { defaultWord } = decoData;
const wordCloud = ref<HTMLElement | null>(null);

onMounted(async () => {
  const result = (await getSelectedMessageList()) as { message: string }[];
  const myChart = echarts.init(wordCloud.value as HTMLElement);
  const maskImg = new Image();
  maskImg.src = word;
  maskImg.crossOrigin = 'anonymous'; // 关键设置
  let data = [];
  const mergedData = [...(defaultWord || []), ...(result?.map((item) => item.message) || [])];
  data = mergedData.map((item: any) => ({
    name: item || '未知词汇',
    value: Math.random() * 100,
  }));

  const option = {
    series: [
      {
        width: '90%',
        height: '90%',
        type: 'wordCloud',
        shape: 'triangle-forward',
        sizeRange: [1, 15],
        rotationRange: [-90, 90],
        maskImage: maskImg,
        rotationStep: 1,
        gridSize: 1,
        drawOutOfBound: false,
        keepAspect: true,
        textStyle: {
          fontWeight: 'bold',
          color: '#8776AB',
        },
        data: data.sort(function (a, b) {
          return b.value - a.value;
        }),
      },
    ],
  };
  maskImg.onload = function () {
    myChart.setOption(option);
  };
});
</script>
<style lang="scss" scoped>
.word-cloud {
  width: 3.12rem;
  height: 2.58rem;
  background: url(../assets/mission/word.png) no-repeat;
  background-size: 100% 100%;
}
</style>
