<template>
  <popup v-model:show="showPopup" :close-on-click-overlay="false" @opened="opendDialog">
    <div class="dialog">
      <div class="close_icon" @click="closeDialog"></div>
      <div class="title">邮寄地址</div>
      <div class="rule-box">
        <div class="mask" v-if="isShowAddress"></div>
        <VanField v-model="form.realName" required label="姓名：" maxlength="20" input-align="right"></VanField>
        <VanField v-model="form.mobile" required label="电话：" maxlength="11" type="tel" input-align="right"></VanField>
        <VanField v-model="form.addressCode" required label="省市区：" readonly @click="addressSelects = true" input-align="right"></VanField>
        <VanField v-model="form.address" required label="详细地址：" maxlength="100" input-align="right"></Van<PERSON>ield>
        <img class="btn" v-if="!isShowAddress" @click="handleSave" src="//img10.360buyimg.com/imgzone/jfs/t1/354989/37/4722/16530/69082ef5F3ba5a6b9/0539bd57a86f1915.png" alt="" />
      </div>
    </div>
  </popup>
  <VanPopup teleport="body" v-model:show="addressSelects" position="bottom">
    <VanArea :area-list="areaList" @confirm="confirmAddress" @cancel="addressSelects = false"></VanArea>
  </VanPopup>
</template>

<script setup lang="ts">
import { Popup, Icon } from 'vant';
import { computed, defineEmits, defineProps, inject, reactive, ref } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';
import { areaList } from '@vant/area-data';
import { containsEmoji, containsSpecialChars, isPhoneNumber, validateDataWithRules } from '@/utils/platforms/validator';
import { userAddressInfo } from '../script/ajax';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  isShowPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  addressId: {
    type: String,
    required: true,
    default: '',
  },
  addressInfo: {
    type: Object,
    required: false,
    default: () => ({}),
  },
});
const form = reactive({
  realName: '',
  mobile: '',
  address: '',
  city: '',
  addressCode: '',
  county: '',
  province: '',
});
const addressSelects = ref(false);
const confirmAddress = (addressItemList: any) => {
  form.province = addressItemList.selectedOptions[0].text;
  form.city = addressItemList.selectedOptions[1].text;
  form.county = addressItemList.selectedOptions[2].text;
  form.addressCode = `${addressItemList.selectedOptions[0].text}/${addressItemList.selectedOptions[1].text}/${addressItemList.selectedOptions[2].text}`;
  addressSelects.value = false;
};
const ruleValidate = {
  realName: [
    {
      required: true,
      message: '请输入姓名',
    },
    {
      validator: containsSpecialChars,
      message: '姓名不能包含特殊字符',
    },
    {
      validator: containsEmoji,
      message: '姓名不能包含表情',
    },
  ],
  mobile: [
    {
      required: true,
      message: '请输入电话',
    },
    {
      validator: isPhoneNumber,
      message: '请输入正确的电话',
    },
  ],
  province: [
    {
      required: true,
      message: '请选择省市区',
    },
  ],
  address: [
    {
      required: true,
      message: '请输入详细地址',
    },
    {
      validator: containsEmoji,
      message: '详细地址不能包含表情',
    },
  ],
};

const showPopup = computed(() => props.isShowPopup);
const emits = defineEmits(['closeDialog']);
const closeDialog = () => {
  Object.assign(form, {
    realName: '',
    mobile: '',
    address: '',
    city: '',
    addressCode: '',
    county: '',
    province: '',
  });
  emits('closeDialog');
};
const isShowAddress = ref(false);
const opendDialog = async () => {
  if (props.addressInfo && props.addressInfo.mobile) {
    form.realName = props.addressInfo.realName;
    form.mobile = props.addressInfo.mobile;
    form.address = props.addressInfo.address;
    form.province = props.addressInfo.province;
    form.city = props.addressInfo.city;
    form.county = props.addressInfo.county;
    form.addressCode = `${props.addressInfo.province}/${props.addressInfo.city}/${props.addressInfo.county}`;
    isShowAddress.value = true;
  } else {
    isShowAddress.value = false;
  }
};
const handleSave = async () => {
  if (!validateDataWithRules(ruleValidate, form)) return;
  console.log('保存地址');
  const result = await userAddressInfo({ ...form, addressId: props.addressId });
  if (result) closeDialog();
};
</script>
<style lang="scss" scoped>
.dialog {
  overflow: hidden;
  width: 6.86rem;
  height: 8.71rem;
  background: url(../assets/dialog/dialog-bg.png) no-repeat;
  background-size: contain;
  padding: 0.88rem 0.35rem;
  box-sizing: border-box;
  font-family: 'OPPOSansR';

  .close_icon {
    position: absolute;
    right: 0.7rem;
    top: 0;
    width: 1rem;
    height: 1rem;
  }

  .title {
    font-size: 0.48rem;
    font-family: 'TsangerYuYangT';
    font-style: italic;
    background: radial-gradient(circle at center, #38286d 0%, #8b67a0 17%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
  }
  .rule-box {
    margin-top: 0.4rem;
    max-height: 6.2rem;
    margin: 0.4rem 0.3rem 0;
    position: relative;
    overflow-y: auto;
    font-size: 0.25rem;
    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0);
      z-index: 1;
    }
    .van-cell {
      border-radius: 0.3rem;
      margin-bottom: 0.2rem;
      --van-cell-font-size: 0.25rem;
    }
    .van-field__control.van-field__control--right,
    .van-field__body {
      background-color: transparent !important;
    }
    .btn {
      width: 4.24rem;
      margin: 1rem auto 0;
    }
  }
}
</style>
