<template>
  <div class='bg' :style='{ backgroundColor: decoData.actBgColor }'>
    <div class="header">
      <img class="kv" :src="decoData.actBg" alt="">
      <img class="rule-btn" :src="decoData.ruleBtn" @click="showRulePopup" alt="">
      <img class="record-btn" :src="decoData.recordBtn" @click="showMyPrizePopup" alt="">
    </div>
    <div class="swiper-container">
     <img class="swiper-title" :src="decoData.bannerBg" alt="">
     <div class="swiper-wrapper">
        <div class="swiper-slide prize-item" v-for="(item, index) in decoData.iconList" :key="index">
          <img :src="item.icon" alt="" @click="iconClick(item, index)" class="prize-img" />
        </div>
      </div>
    </div>
    <div class="gifts">
      <img class="gift-title" :src="decoData.title" alt="">
      <div class="gift-list">
        <div class="gift-item" v-for="(item, index) in prizeList">
          <img class="gift-bg" :src="item.moduleDesign" alt="">
          <img @click="confirmPrize(item)" class="gitt-btn" :class="{ 'gitt-btn-disabled': item.status == 2}" :src="decoData.receiveBtn" alt="">
        </div>
      </div>
    </div>
      <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="center" :close-on-click-overlay="false">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="center" :close-on-click-overlay="false">
    <MyPrize v-if="showMyPrize" :myPrizeList="myPrizeList" @close="showMyPrize = false" @showCardNum="showCardNum"></MyPrize>
  </VanPopup>
  <!-- 警告弹窗 -->
  <VanPopup teleport="body" v-model:show="showWarning" position="center" :close-on-click-overlay="false">
    <Warning @close="showWarning = false"></Warning>
  </VanPopup>
  <!-- 确认弹窗 -->
  <VanPopup teleport="body" v-model:show="showConfirm" position="center" :close-on-click-overlay="false">
    <Confirm :prizeInfo="prizeInfo" @close="showConfirm = false" @confirm="receivePrize(prizeInfo.prizeId)"></Confirm>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward" position="center" :close-on-click-overlay="false">
    <Award :decoData="decoData" @showGuide="handleShowGuide" :awardPrize="awardPrize" @close="closeAward"></Award>
  </VanPopup>
  <!-- 引导弹窗 -->
  <VanPopup teleport="body" v-model:show="showGuide" position="center" :close-on-click-overlay="false">
    <Guide :guide="guide" @close="showGuide = false"></Guide>
  </VanPopup>
  </div>

</template>

<script setup lang='ts'>
import { inject, nextTick, onMounted, ref } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import { DecoData } from '@/types/DecoData';
import 'swiper/swiper.min.css';
import { httpRequest } from '@/utils/service';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { lzReportClick } from '@/utils/trackEvent/lzReport';

import { BaseInfo } from '@/types/BaseInfo';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import Warning from '../components/Warning.vue';
import Confirm from '../components/Confirm.vue';
import Award from '../components/Award.vue';
import Guide from '../components/Guide.vue';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const prizeList = ref<any>([]);
const showRule = ref(false);
const ruleTest = ref('');
const prizeInfo = ref<any>({});
const awardPrize = ref<any>({});
const showMyPrize = ref(false);
const myPrizeList = ref<any>([]);
const showAward = ref(false);
const showConfirm = ref(false);
const showWarning = ref(false);
const showGuide = ref(false);
const guide = ref('');
let prizeSwiper: Swiper;
// 初始化轮播图
const initSwiper = () => {
  nextTick(() => {
    if (prizeSwiper) {
      prizeSwiper.destroy();
    }
    
    // 只有当 iconList 超过4个时才启用自动滚动和循环
    const shouldAutoplay = decoData.iconList && decoData.iconList.length > 4;
    
    prizeSwiper = new Swiper('.swiper-container', {
      autoplay: shouldAutoplay ? {
        delay: 1000,
        stopOnLastSlide: false,
        disableOnInteraction: false,
      } : false,
      loop: shouldAutoplay,
      slidesPerView: 4,
      loopedSlides: shouldAutoplay ? 8 : 0,
      spaceBetween: 10,
    });
  });
};

// 点击图标
const iconClick = (item: any, index: number) => {
  lzReportClick(`icon${index+1}`)
  if (item.url) {
    window.open(item.url, '_blank');
  }
}
// 活动检查（统一处理toast和弹窗显示）
const activityCheck = async () => {
  // 活动未开始
  if (baseInfo.status === 1) {
    closeToast(); // 关闭可能存在的loading
    showToast('活动未开始');
    return false;
  }
  // 活动已结束
  if (baseInfo.status === 3) {
    closeToast(); // 关闭可能存在的loading
    showToast('活动已结束');
    return false;
  }
  // 检查是否是中信员工
  const status = await checkStatus();
  if (!status) {
    closeToast(); // 关闭可能存在的loading
    showWarning.value = true;
    return false;
  }
  return true;
}

// 初始化
onMounted(async () => {
  initSwiper();
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0, // 不自动关闭
  });
  
  try {
    // 获取奖品列表
    await getPrizeList();
    // 活动检查（内部会处理toast关闭和显示）
    const checkResult = await activityCheck();
    if (checkResult) {
      // 检查通过，关闭loading
      closeToast();
    }
    // 检查不通过时，activityCheck内部已经处理了toast关闭和显示
  } catch (error) {
    closeToast();
    showToast('加载失败，请稍后重试');
  }
});

// 展示卡密弹窗
const showCardNum = (item: any) => {
  awardPrize.value = item;
  showAward.value = true;
}

// 判断是否是中信员工
const checkStatus = async () => {
  try {
    const { data } = await httpRequest.post('90203/check/crowd');
    return data;
  } catch (error: any) {
    return false
  }
};
// 获取奖品列表
const getPrizeList = async () => {
  try {
    const {data} = await httpRequest.post('90203/prize/list');
    prizeList.value = data;
  } catch (error) {
    console.error(error);
  }
}
// 领取奖品
const receivePrize = async (prizeId: string) => {
  // 校验规则
  // 确认领取 -> 领取 -> 领取成功
  try {
    showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
    const {data} = await httpRequest.post(`90203/prize/receive`, {prizeId});
    if (data.status === 1) {
      awardPrize.value = data;
      showConfirm.value = false;
      showAward.value = true;
      closeToast();
    } else {
      showConfirm.value = false;
      showToast('领取失败，请联系客服');
      showConfirm.value = false;
    }
  } catch (error: any) {
    showConfirm.value = false;
    showToast('领取失败，请联系客服');
    showConfirm.value = false;
  } finally {
    await getPrizeList();
  }
}
// 关闭中奖弹窗
const closeAward = async () => {
  showAward.value = false;
  await getPrizeList();
}

// 检查是否在领取时间内
const checkYetTime = async (prizeId: string) => {
  try {
    const {data} = await httpRequest.post('90203/prize/receive/status', {prizeId});
    return data;
  } catch (error: any) {
    showToast(error.message);
    return false;
  }
}
// 确认领取奖品
const confirmPrize = async (item: any) => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  
  const { status, prizeId } = item;
  if (status == 2) {
    return;
  }

  // 活动检查（内部会处理toast关闭和显示）
  const checkResult = await activityCheck();
  if (!checkResult) {
    // 检查不通过时，activityCheck内部已经处理了toast关闭和显示
    return;
  }

  // 检查是否在领取时间内
  const yetTimeResult = await checkYetTime(prizeId);
  if (!yetTimeResult) {
    return;
  }
  
  prizeInfo.value = prizeList.value.find((item: any) => item.prizeId === prizeId);
  showConfirm.value = true;
  closeToast();
}
// 展示我的奖品弹窗
const showMyPrizePopup = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  const {data} = await httpRequest.post('90203/prize/my');
  myPrizeList.value = data;
  showMyPrize.value = true;
  closeToast();
}

// 展示活动规则弹窗
const showRulePopup = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  const {data} = await httpRequest.get('/common/getRule');
  ruleTest.value = data;
  showRule.value = true;
  closeToast();
}

// 展示引导弹窗
const handleShowGuide = (value: string) => {
  guide.value = value;
  showGuide.value = true;
}

</script>

<style scoped lang='scss'>


.bg {
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
}

.header {
  position: relative;
  .kv {
    width: 100%;
    display: block;
    object-fit: contain;
  }
  .rule-btn {
    position: absolute;
    top: 2.4rem;
    right: 0;
    width: 100%;
    display: block;
    object-fit: contain;
    width: 1.1rem;
  }
  .record-btn {
    position: absolute;
    top: 3.05rem;
    right: 0;
    width: 100%;
    display: block;
    object-fit: contain;
    width: 1.1rem;
  }
}
.swiper-container {
  box-sizing: border-box;
  width: 100%;
  padding: 0 0.2rem;

  .swiper-title {
    width: 100%;
    display: block;
    object-fit: contain;
  }
  
  .swiper-wrapper {
    margin-top: .15rem;
  }
  
  .swiper-slide {
    width: 1.65rem;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .prize-img {
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }
}
.gifts {
  width: 100%;
  box-sizing: border-box;
  padding: 0 0.2rem 1rem;

  .gift-title {  margin: 0 auto;
    width: 6rem;
    display: block;
    object-fit: contain;
  }
  .gift-list {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;

    .gift-item {
      position: relative;
      .gift-bg {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      .gitt-btn {
        position: absolute;
        top: .85rem;
        right: .48rem;
        width: 1.2rem;
        object-fit: contain;
        
        &.gitt-btn-disabled {
          filter: grayscale(100%);
          opacity: 0.5;
          pointer-events: none;
        }
      }
    }
  }
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
