<template>
  <div class="taskListStyleAll">
    <div class="closeDiv" @click="closeClick()"></div>
    <div class="listDiv">
      <div class="itemDiv" v-for="(item, index) in taskList" :key="index">
        <div class="taskLeftDiv">
          <div class="taskLeftTopDiv">
            <div class="taskTitleDiv">
              {{ taskPrompt(item.taskType, item)?.taskTittle }}
            </div>
            <div class="taskNumDiv" v-if="item.taskType !== 8">
              <span v-if="!isPreview">（{{ item.taskFinishCount }}/{{ item.limit }}）</span>
              <span v-else>（0/1）</span>
            </div>
          </div>
          <div class="taskLeftBottomDiv">
            {{ taskPrompt(item.taskType, item)?.taskText }}
          </div>
        </div>
        <div class="taskRightDiv">
          <div
            v-if="item.taskType === 8 || !isPreview && item.taskFinishCount < item.limit || isPreview"
            class="btnDiv"
            @click="doTaskBtnClick(item)"
          >
            {{ taskPrompt(item.taskType, item)?.btnName }}
          </div>
          <div v-else class="btnGrayDiv" @click="doTaskBtnClick(item)">已完成</div>
        </div>
      </div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showBrowseSkuPop" position="bottom">
    <BrowseSkuPop
      :itemTaskData="itemTaskData"
      @close="showBrowseSkuPop = false"
    ></BrowseSkuPop>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showAddCartSkuPop" position="bottom">
    <AddCartSkuPop
      :itemTaskData="itemTaskData"
      @addCartSuccessEmit="addCartSuccessEmit"
      @close="showAddCartSkuPop = false"
    ></AddCartSkuPop>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showBuySkuPop" position="bottom">
    <BuySkuPop
      v-if="showBuySkuPop"
      :itemTaskData="itemTaskData"
      @close="showBuySkuPop = false"
    ></BuySkuPop>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showGetPrizeCodePop" position="center">
    <GetPrizeCodePop
      v-if="showGetPrizeCodePop"
      :prizeCodeList="prizeCode"
      @close="showGetPrizeCodePop = false"
    ></GetPrizeCodePop>
  </VanPopup>
</template>
<script lang="ts" setup>
import { isPreview } from "@/utils";
import { httpRequest } from "@/utils/service";
import { closeToast, showLoadingToast, showToast } from "vant";
import { inject, onMounted, onUnmounted, ref, watch } from "vue";
import type { BaseInfo } from "@/types/BaseInfo";
import BrowseSkuPop from "./BrowseSku.vue";
import AddCartSkuPop from "./AddCartSku.vue";
import BuySkuPop from "./BuySku.vue";
import GetPrizeCodePop from "./GetPrizeCode.vue";
import dayjs from "dayjs";

const showGetPrizeCodePop = ref(false);
const prizeCode = ref<any>([]); // 做任务获取的中奖码
const showBuySkuPop = ref(false);
const showAddCartSkuPop = ref(false);
const showBrowseSkuPop = ref(false);
const baseInfo = inject("baseInfo") as BaseInfo;
const itemTaskData = ref(null);
const props = defineProps({
  taskListData: {
    type: Array,
    default: [],
  },
});
const taskPrompt = (taskType: number, itemData: any) => {
  switch (taskType) {
    case 13:
      return {
        taskTittle: "加入会员",
        taskText: `可获得${itemData.perLotteryCount}张中将码，活动期间可参与1次`,
        btnName: "去入会",
      };
    case 14:
      return {
        taskTittle: "每日签到",
        taskText: `可获得${itemData.perLotteryCount}张中将码，每日可参与1次`,
        btnName: "去签到",
      };
    case 29:
      return {
        taskTittle: "发布你的vivo手机出片美图",
        taskText: `可获得${itemData.perLotteryCount}张中将码，每日可参与${itemData.dailyLimit}次`,
        btnName: "去发布",
      };
    case 30:
      return {
        taskTittle: "邀请好友为照片点赞",
        taskText: `可获得${itemData.perLotteryCount}张中将码，每日可参与${itemData.dailyLimit}次`,
        btnName: "去邀请",
      };
    case 28:
      return {
        taskTittle: `浏览vivo新品${itemData.taskSeconds}秒`,
        taskText: `可获得${itemData.perLotteryCount}张中将码，每日可参与${itemData.dailyLimit}次`,
        btnName: "去浏览",
      };
    case 7:
      return {
        taskTittle: `加购vivo新品`,
        taskText: `可获得${itemData.perLotteryCount}张中将码，每日可参与${itemData.dailyLimit}次`,
        btnName: "去加购",
      };
    case 8:
      return {
        taskTittle: `下单vivo新品`,
        taskText: `可获得${itemData.perLotteryCount}张中将码，活动期间不限参与次数`,
        btnName: "去下单",
      };
    case 4:
      return {
        taskTittle: `浏览${itemData.pageName}${itemData.taskSeconds}秒`,
        taskText: `可获得${itemData.perLotteryCount}张中将码，每日可参与${itemData.dailyLimit}次`,
        btnName: "去浏览",
      };
    default:
      return null;
  }
};
const emits = defineEmits(["close"]);
const taskList = ref<any>([]);

const closeClick = () => {
  emits("close", null);
};
const getTaskListData = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/getTask");
    console.log(res, "任务列表==========");
    closeToast();
    taskList.value = res.data;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
// 做签到任务
const dailySign = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/dailySign");
    prizeCode.value = res.data;
    await getTaskListData();
    showGetPrizeCodePop.value = true;
    closeToast();
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
//浏览商品成功回调
const browseSuccessEmit = async (data: any) => {
  console.log(data, "浏览商品成功回调=======");
  showBrowseSkuPop.value = false;
  if (data) {
    try {
      showLoadingToast({
        message: "加载中...",
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post("/96012/browseSku/browseSku", {
        skuId: data.skuId,
      });
      if (res.data && res.data.length > 0) {
        prizeCode.value = res.data;
        showGetPrizeCodePop.value = true;
      }
      await getTaskListData();
    } catch (error: any) {
      showToast(error.message);
    }
  }
};
// 一键加购成功回调
const addCartSuccessEmit = async (datas: any) => {
  console.log(datas, datas.length, "一键加购成功回调=======");
  showAddCartSkuPop.value = false;
  if (datas && datas.length > 0) {
    prizeCode.value = datas;
    showGetPrizeCodePop.value = true;
  }
  await getTaskListData();
};
//浏览会场成功回调
const browsLive = async (taskId: any) => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/browseLive/execute", {
      taskId: taskId,
    });
    prizeCode.value = res.data;
    showGetPrizeCodePop.value = true;
    console.log(res, "浏览会场成功==========");
    closeToast();
    await getTaskListData();
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
// 浏览任务检查时长
const initTask = async () => {
  const task = window.localStorage.getItem("doTask");
  if (!task) return;
  window.localStorage.removeItem("doTask");
  const newItem = JSON.parse(task);
  const newTime: number = dayjs().valueOf(); // 当前时间戳
  const oldTime: number = newItem.time; // 做任务的时间
  const { taskType, skuId, taskSeconds, taskId } = newItem;
  console.log(taskType, skuId, taskSeconds, taskId, "taskList检查任务状态=========");
  const num = ref(0); // 需要做任务满足时长
  if (taskType === 4) {
    // num.value = taskSeconds.value * 1000;
    const downTime = newTime - oldTime >= taskSeconds;
    if (downTime) {
      browsLive(taskId);
    } else {
      showToast({
        message: "浏览时间不足，不能获得奖励哦~",
        duration: 2000,
        forbidClick: true,
        onClose: () => {},
      });
    }
  } else if (taskType === 28) {
    // num.value = taskSeconds.value * 1000;
    const downTime = newTime - oldTime >= taskSeconds;
    if (downTime) {
      browseSuccessEmit({
        skuId: skuId,
      });
    } else {
      showToast({
        message: "浏览时间不足，不能获得奖励哦~",
        duration: 2000,
        forbidClick: true,
        onClose: () => {},
      });
    }
  }
};
// 任务按钮点击
const doTaskBtnClick = (itemData: any) => {
  if (isPreview && itemData.taskType !== 7 && itemData.taskType !== 8 && itemData.taskType !== 28) {
    showToast("活动预览，仅供查看");
    return;
  }
  if (itemData.taskFinishCount >= itemData.limit) {
    // 任务已完成
    return;
  }
  switch (itemData.taskType) {
    case 13:
      //TODO
      window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(
        `${window.location.href}&isJoin=1`
      )}`;
      break;
    case 14:
      // 签到 TODO
      dailySign();
      break;
    case 29:
      emits("close", "taskUpload");
      break;
    case 30:
      emits("close", "taskInviteShare");
      break;
    case 28:
      itemTaskData.value = itemData;
      showBrowseSkuPop.value = true;
      break;
    case 7:
      itemTaskData.value = itemData;
      showAddCartSkuPop.value = true;
      break;
    case 8:
      itemTaskData.value = itemData;
      showBuySkuPop.value = true;
      break;
    case 4:
      const oldTime = dayjs().valueOf();
      localStorage.setItem(
        "doTask",
        JSON.stringify({
          taskType: itemData.taskType,
          taskId: itemData.taskId,
          time: oldTime,
          taskSeconds: Number(itemData.taskSeconds) * 1000,
        })
      );
      window.location.href = itemData.pageLink;
      // window.location.assign(
      //   "https://shopmember.m.jd.com/shopcard/?venderId=739130&shopId=734259&channel=801"
      // );
      break;
  }
};
const initData = async () => {
  if (!isPreview) {
    await getTaskListData();
    await initTask();
  } else {
    taskList.value = props.taskListData;
    console.log(taskList.value, "任务列表==========")
    console.log(props.taskListData, "任务列表==========")
  }
};

const handleVisiable = async (e: any) => {
  console.log(
    document.visibilityState,
    "taskList如果缓存中存在flag再执行判断visibilityState======="
  );
  // 如果缓存中存在flag再执行判断visibilityState
  if (document.visibilityState !== "visible") return;
  await initTask();
};
// 在preview模式下监听props.taskListData的变化
watch(() => props.taskListData, (newTaskListData) => {
  if (isPreview && newTaskListData) {
    taskList.value = newTaskListData;
    console.log('taskList updated from props:', newTaskListData);
  }
}, { deep: true, immediate: true });

onMounted(() => {
  initData();
  document.addEventListener("visibilitychange", handleVisiable);
});
onUnmounted(() => {
  document.removeEventListener("visibilitychange", handleVisiable);
});
</script>
<style lang="scss" scoped>
.taskListStyleAll {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/343860/9/23237/12754/690aac61Fed2b27b2/4f278b4398bcef82.png);
  background-size: cover;
  width: 7.5rem;
  max-height: 12.43rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.42rem;
  .closeDiv {
    position: absolute;
    right: 0.38rem;
    top: 0.44rem;
    width: 0.3rem;
    height: 0.3rem;
    // background-color: red;
  }
  .listDiv {
    padding-bottom: 0.3rem;
    max-height: 11rem;
    overflow-y: scroll;
    overflow-x: hidden;
    .itemDiv {
      border-radius: 0.15rem;
      background-color: #fbf6ed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.2rem 0.2rem;
      width: 6.9rem;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: 0.1rem;
      .taskLeftDiv {
        max-width: 5rem;
        .taskLeftTopDiv {
          display: flex;
          align-items: center;
          font-size: 0.3rem;
          color: #000000;
        }
        .taskLeftBottomDiv {
          font-size: 0.24rem;
          color: #666666;
        }
      }
      .taskRightDiv {
        .btnDiv {
          border-radius: 0.4rem;
          background-color: #000000;
          color: #ffffff;
          font-size: 0.24rem;
          padding: 0.14rem 0.3rem;
          box-sizing: border-box;
          cursor: pointer;
        }
        .btnGrayDiv {
          border-radius: 0.4rem;
          background-color: gray;
          color: #ffffff;
          font-size: 0.24rem;
          padding: 0.14rem 0.3rem;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
