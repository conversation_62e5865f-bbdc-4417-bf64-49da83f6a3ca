<template>
  <div class="rule-bk">
    <div class="close" @click="close" />
    <div class="mainTabArea">
      <div class="mainTabWrapper" v-for="(tab, index) in tabList" :key="index">
        <div
          class="mainTab"
          :id="`${activeMineTab}`"
          :style="index === activeMineTab ? 'color: #000' : 'color:#666'"
          @click="switchMineTab(index, tab.periodId)"
        >
          {{ tab.periodTitle }}
        </div>
        <!-- 选中指示器 -->
        <div
          :id="`${activeMineTab}`"
          :class="index === activeMineTab ? 'activeTabIndicator' : 'tabIndicator'"
        />
      </div>
    </div>
    <div class="prizeTitle">
      <div class="time">中奖码</div>
      <div class="name">奖品</div>
      <div class="status">操作</div>
    </div>
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div class="prizeCode">
          <div>{{ item.prizeCode }}</div>
          <div class="prizeCodeDate">
            <div class="date">{{ formatDate(item.prizeCodeDate || "") }}</div>
            <div class="time">{{ formatTime(item.prizeCodeDate || "") }}</div>
          </div>
        </div>
        <div
          class="name"
          :class="
            item.prizeName === '未开奖' ||
            item.prizeName === '未中奖' ||
            item.prizeName === '发奖失败'
              ? 'grayName'
              : ''
          "
        >
          {{ item.prizeName }}
        </div>
        <div class="status" v-if="item.prizeType === 3">
          <div class="blackBtn" v-if="item.writeAddress" @click="changAddress(item)">
            已填写
          </div>
          <div class="blackBtn" v-else @click="changAddress(item)">填写地址</div>
        </div>
        <div class="status" v-else-if="item.prizeType === 7">
          <div class="blackBtn copy-btn" :copy-text="getCopyText(item.prizeContent)">
            复制卡密
          </div>
        </div>
        <div class="status" v-else>
          <div
            class="green"
            v-if="
              item.prizeName !== '未开奖' &&
              item.prizeName !== '未中奖' &&
              item.prizeName !== '发奖失败'
            "
          >
            已发放
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">抱歉！暂无中奖码信息~</div>
    </div>
  </div>
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="center">
    <SaveAddress
      v-if="showSaveAddress"
      :addressId="addressId"
      :activityPrizeId="activityPrizeId"
      :echoData="echoData"
      @close="closeSaveAddress"
    ></SaveAddress>
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { inject, reactive, ref } from "vue";
import { showLoadingToast, closeToast, showToast } from "vant";
import SaveAddress from "./SaveAddress.vue";
import { httpRequest } from "@/utils/service";
import { FormType } from "../ts/type";
import Clipboard from "clipboard";
import { BaseInfo } from "@/types/BaseInfo";

const baseInfo = inject("baseInfo") as BaseInfo;
const isPreview = inject("isPreview") as boolean;

const emits = defineEmits(["close"]);

const close = () => {
  emits("close");
};
const tabList = ref([
  // {
  //   periodTitle: "2025.6.24期",
  //   periodId: 0,
  // },
  {
    periodTitle: "本期",
    periodId: 1,
  },
]);
const prizeType = {
  0: "谢谢参与",
  1: "优惠券",
  2: "京豆",
  3: "实物",
  4: "积分",
  5: "专享价",
  6: "红包",
  7: "礼品卡",
  8: "京东e卡",
  9: "PLUS会员卡",
  10: "爱奇艺会员卡",
  11: "自营令牌促销",
  12: "京元宝",
};

interface Prize {
  activityPrizeId?: string;
  address?: string;
  addressId?: string;
  city?: string;
  county?: string;
  createTime?: string;
  deliverName?: string;
  deliverNo?: string;
  deliveryStatus?: number;
  isFuLuWaitingReceive?: boolean;
  mobile?: string;
  prizeCode?: number;
  prizeCodeDate?: string;
  prizeContent?: string;
  prizeImg?: string;
  prizeName?: string;
  prizeType: number;
  province?: string;
  realName?: string;
  recordId?: string;
  userPrizeId?: string;
  writeAddress?: boolean;
}

const prizes = reactive([] as Prize[]);

const getUserPrizes = async (periodId) => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/myPrizeCode", { periodId });
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

// 添加tab选中状态
const activeMineTab = ref(0);
const activePeriodId = ref(0);
// 主tab切换
const switchMineTab = (index: number, periodId: number) => {
  activeMineTab.value = index;
  activePeriodId.value = periodId;
  getUserPrizes(periodId);
};

// 获取所有活动期数
const getTabList = async () => {
  try {
    const res = await httpRequest.post("/96012/getPeriodInfo");
    tabList.value = res.data;
    activeMineTab.value = res.data.length - 1;
    activePeriodId.value = res.data[res.data.length - 1].periodId;

    getUserPrizes(activePeriodId.value);
  } catch (error: any) {
    console.error(error);
  }
};

if (!isPreview) {
  getTabList();
}

/**
 * 复制礼品卡信息
 * @param content
 */
const getCopyText = (content: any) => {
  let text = "";
  const prizeContent = JSON.parse(content);
  if (prizeContent.cardNumber && prizeContent.cardPassword) {
    text = `卡号：${prizeContent.cardNumber}\n卡密：${prizeContent.cardPassword}`;
  } else if (prizeContent.cardNumber && !prizeContent.cardPassword) {
    text = `卡号：${prizeContent.cardNumber}`;
  }
  return text;
};

const clipboard = new Clipboard(".copy-btn", {
  text(trigger) {
    return trigger.getAttribute("copy-text") ?? "";
  },
})
  .on("success", () => {
    showToast("复制成功");
  })
  .on("error", () => {
    showToast("复制失败");
  });

const showSaveAddress = ref(false);
const addressId = ref("");
const activityPrizeId = ref("");
const echoData: FormType = reactive({
  realName: "",
  mobile: "",
  province: "",
  city: "",
  county: "",
  address: "",
  writeAddress: false,
});

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast("活动已结束");
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.recordId;
  Object.keys(echoData).forEach((key) => {
    echoData[key as keyof FormType] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};

// 格式化日期 YYYY-MM-DD
const formatDate = (dateString: string) => {
  if (!dateString) return "";
  return dayjs(dateString).format("YYYY-MM-DD");
};

// 格式化时间 hh:mm:ss
const formatTime = (dateString: string) => {
  if (!dateString) return "";
  return dayjs(dateString).format("HH:mm:ss");
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/356472/11/3024/13757/6909c2dfFfc4079f4/f83ed078c251abf6.png);
  background-size: 100% 100%;
  width: 6.5rem;
  height: 10.53rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 2rem;

  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.79rem;
    height: 0.79rem;
  }

  .mainTabArea {
    width: 5.3rem;
    height: 0.68rem;
    background-color: #ffffff;
    border-radius: 0.34rem;
    margin: 0 auto 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    overflow-x: scroll;
    font-size: 0.24rem;
    padding: 0.1rem 0 0 0;
    &::-webkit-scrollbar {
      display: none;
    }

    .mainTabWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;

      .mainTab {
        width: 2.65rem;
        height: 0.4rem;
        line-height: 0.4rem;
        text-align: center;
        font-weight: bold;
        cursor: pointer;
      }

      .tabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
        background-color: transparent;
      }
      .activeTabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
        background-color: #415fff;
      }
    }
  }
  .prizeTitle {
    padding: 0 0.4rem 0.2rem;
    color: #666666;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.24rem;
    .time,
    .status {
      width: 30%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 0.4rem;
    }
    .name {
      width: 32%;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 0.4rem;
    }
  }
  .content {
    height: 6.6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    padding: 0 0.4rem;
    .prize {
      padding: 0 0 0.2rem;
      color: #000;
      display: flex;
      /* align-items: center; */
      justify-content: space-between;
      .prizeCode {
        width: 30%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 0.4rem;
        font-weight: bold;
        .prizeCodeDate {
          font-size: 0.18rem;
          color: #999999;
          text-align: left;
          font-weight: normal;
          width: 1rem;
          margin: 0 auto;
          line-height: 0.3rem;
        }
      }
      .name {
        width: 32%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 0.4rem;
      }
      .grayName {
        color: #666;
      }
      .status {
        width: 30%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 0.4rem;
      }
      .blackBtn {
        width: 1.28rem;
        height: 0.5rem;
        background-color: #000000;
        color: #ffffff;
        border-radius: 0.25rem;
        margin: 0 auto;
        font-size: 0.24rem;
        line-height: 0.5rem;
      }
    }

    .no-data {
      text-align: center;
      line-height: 5.2rem;
      font-size: 0.24rem;
      color: #666666;
    }
  }
}
</style>
