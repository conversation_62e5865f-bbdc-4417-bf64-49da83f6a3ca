<template>
  <div class="dialog-content">
    <div class="closeBox">
      <div class="close" @click="closePopup" />
    </div>
    <div class="rule-bk">
      <div class="content">
        <!-- 当有多张图片时，使用Swiper -->
        <van-swipe
          v-if="imgDetail.imgList.length > 1"
          class="my-swipe"
          indicator-color="white"
        >
          <van-swipe-item v-for="(item, index) in imgDetail.imgList" :key="index">
            <img
              class="imgs"
              v-if="item.imgUrl"
              :src="item.imgUrl"
              alt=""
              @click="handleVideo"
            />
            <img class="imgs" v-else :src="item" alt="" />
            <div v-if="item.videoUrl && item.videoUrl !== ''" class="video-container">
              <video
                id="video-source"
                playsinline
                poster=""
                :class="!isShowPlayBtn ? 'isPlaying' : ''"
                controls
                controlslist="noremoteplayback foobar"
                preload="metadata"
                :src="item.videoUrl"
              ></video>
              <img
                class="palyBtn"
                v-if="isShowPlayBtn"
                @click="handleVideo"
                src="https://img10.360buyimg.com/imgzone/jfs/t1/331179/15/21235/2725/68e7c8f7F7840cef6/2e410c6677ce5029.png"
                alt=""
              />
            </div>
          </van-swipe-item>
        </van-swipe>
        <div v-else v-for="(item, index) in imgDetail.imgList" :key="index">
          <img class="imgs" v-if="item.imgUrl" :src="item.imgUrl" alt="" />
          <img class="imgs" v-else :src="item" alt="" />
          <div v-if="item.videoUrl && item.videoUrl !== ''" class="video-container">
            <video
              id="video-source"
              playsinline
              poster=""
              :class="!isShowPlayBtn ? 'isPlaying' : ''"
              controls
              controlslist="noremoteplayback foobar"
              preload="metadata"
              :src="item.videoUrl"
            ></video>
            <img
              class="palyBtn"
              v-if="isShowPlayBtn"
              @click="handleVideo"
              src="https://img10.360buyimg.com/imgzone/jfs/t1/331179/15/21235/2725/68e7c8f7F7840cef6/2e410c6677ce5029.png"
              alt=""
            />
          </div>
        </div>
      </div>
      <div class="titleBox">{{ imgDetail.title }}</div>
      <div class="contentBox">{{ imgDetail.content }}</div>
      <div class="userInfoBox" v-if="showUserInfo">
        <div class="userInfo">
          <div class="avatar">
            <img :src="imgDetail.avatar" alt="" />
          </div>
          <div class="nickName">{{ imgDetail.nickName }}</div>
        </div>
        <div class="fansLikeBox">
          <img
            @click="likeThisPost(imgDetail)"
            class="like"
            :src="
              imgDetail.liked || liked
                ? 'https://img10.360buyimg.com/imgzone/jfs/t1/345820/4/20072/571/69044b4eF37e1c8dd/0ed93cb5a7c0f514.png'
                : 'https://img10.360buyimg.com/imgzone/jfs/t1/344172/34/18852/1210/69044b4fF93ebc1d1/c1129ab8ca68f022.png'
            "
            alt=""
          />
          <div class="fans">{{ imgDetail.fans }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, nextTick, Ref, ref } from "vue";
import { furnish } from "../ts/furnishStyles";
import { httpRequest } from "@/utils/service";
import { closeToast, showToast } from "vant";

const props = defineProps(["imgDetail", "showUserInfo"]);

const emits = defineEmits(["closePop"]);

const isShowPlayBtn: Ref<boolean> = ref(true);
const videoRef = ref<HTMLVideoElement | null>(null);
const isPlayed: Ref<boolean> = ref(false);
const seconds = ref<number>(0);

// 添加缺失的方法
const closePopup = () => {
  emits("closePop");
};

const liked = ref(false);
// 点赞功能实现
const likeThisPost = async (imgDetail: any) => {
  console.log("点赞功能:", imgDetail);
  if (imgDetail.liked) {
    showToast("您已经点过赞了");
  } else {
    try {
      await httpRequest.post("/96012/likeDynamic", {
        dynamicId: imgDetail.dynamicId,
      });
      liked.value = true;
    } catch (error) {
      closeToast();
      showToast(error.message);
      console.log(error);
    }
  }
};

const getMobileModel = (): string => {
  const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera;

  // iPhone模型检测
  if (/iPhone/.test(userAgent) && !window.MSStream) {
    const iPhoneModel = userAgent.match(/iPhone\s+([\w\s]+)/i);
    return iPhoneModel ? "iPhone" : "Unknown iPhone model";
  }

  // Android模型检测
  if (/Android/.test(userAgent)) {
    const androidModel = userAgent.match(/Android\s+([\w\.]+);.*\s([^\s;]+)\sBuild/i);
    return androidModel ? "Android" : "Unknown Android model";
  }

  // 其他设备的通用检测
  return "Unknown device model";
};

// 修改handleVideo函数，添加类型检查
const handleVideo = (item?: any) => {
  if (!videoRef.value) return;

  videoRef.value.play();

  const system = getMobileModel();
  if (system !== "iPhone" && videoRef.value.requestFullscreen) {
    videoRef.value.requestFullscreen();
  }

  isShowPlayBtn.value = false;
  if (isPlayed.value) {
    return;
  }
  isPlayed.value = true;
  // 触发按钮倒计时
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else {
      clearInterval(timer);
    }
  }, 1000);
};
onMounted(() => {
  videoRef.value = document.querySelector("video");
  if (videoRef.value) {
    videoRef.value.addEventListener("webkitendfullscreen", () => {
      console.log("iOS: 视频已退出全屏");
      isShowPlayBtn.value = false;
    });
  }
  document.addEventListener("fullscreenchange", () => {
    if (!document.fullscreenElement && videoRef.value) {
      console.log("非 iOS: 视频已退出全屏");
      isShowPlayBtn.value = false;
    }
  });
});
</script>

<style scoped lang="scss">
.dialog-content {
  height: auto;
}
.closeBox {
  position: relative;
  width: 6.5rem;
  height: 0.9rem;
  .close {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.7rem;
    height: 0.7rem;
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/342765/2/17370/2193/68fb3965F0c739a59/b2fcfc96310a649f.png)
      no-repeat;
    background-size: 100%;
  }
}
.rule-bk {
  width: 6.5rem;
  height: auto;
  max-height: 80vh;
  position: relative;
  margin: 0 auto;
  background: #fff;
  border-radius: 0.3rem;
  padding: 0.3rem;
  overflow-y: scroll;

  .content {
    border-radius: 0.3rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    line-height: 0.35rem;
    color: #666666;
    word-wrap: break-word;
    white-space: pre-wrap;
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 和 Edge */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari 和 Opera */
    }
    :deep(.van-swipe__track) {
      display: flex;
      align-items: center;
      background: #fff9f0;
      border-radius: 0.3rem;
    }
    .imgs {
      margin: 0 auto;
      width: 5.9rem;
      display: block;
      position: relative;
      z-index: 8;
    }
    .video-container {
      position: absolute;
      top: 0.3rem;
      left: 0.3rem;
      width: 5.9rem;
      border-radius: 0.3rem;
      overflow: hidden;
      .isPlaying {
        z-index: 10;
      }
      #video-source {
        width: 5.9rem;
        position: relative;
        top: 0;
        left: 0;
        height: auto;
        border-radius: 0.3rem;
      }
      .palyBtn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 1.6rem;
        height: 1.6rem;
        z-index: 9;
      }
    }
  }
  .titleBox {
    font-size: 0.22rem;
    margin: 0.1rem 0;
  }
  .contentBox {
    font-size: 0.2rem;
    color: #666;
    line-height: 0.24rem;
    word-wrap: break-word;
    width: 5.9rem;
  }
  .userInfoBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.1rem;
    .userInfo {
      width: 2rem;
      align-items: center;
      display: flex;
      .avatar {
        width: 0.5rem;
        height: 0.5rem;
        overflow: hidden;
        border-radius: 50%;
        img {
          width: 0.5rem;
        }
      }
      .nickName {
        font-size: 0.2rem;
        margin-left: 0.1rem;
      }
    }
    .fansLikeBox {
      font-size: 0.2rem;
      margin-left: 0.1rem;
      display: flex;
      align-items: center;
      .like {
        width: 0.36rem;
        height: 0.3rem;
        margin-right: 0.05rem;
      }
      .fans {
        color: #666;
      }
    }
  }
}
</style>
