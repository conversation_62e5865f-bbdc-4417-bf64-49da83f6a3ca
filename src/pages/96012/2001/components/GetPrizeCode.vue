<template>
  <div class="getPrizeCodeDivAll">
    <div class="closeDiv" @click="closeClick()"></div>

    <div class="prizeListDiv">
      <div class="prizeItemDiv" v-for="(item, index) in prizeCodeList" :key="index">
        {{ item }}
      </div>
    </div>
    <div class="myAllPrizeCode" @click="myAllPrizeCodeClick()"></div>
  </div>
  <VanPopup teleport="body" v-model:show="showPrizeCodeAllPop" position="center">
    <PrizeCodeAllPop @close="showPrizeCodeAllPop = false"></PrizeCodeAllPop>
  </VanPopup>
</template>
<script lang="ts" setup>
import { isPreview } from "@/utils";
import PrizeCodeAllPop from "./PrizeCode.vue";
import { ref } from "vue";

const showPrizeCodeAllPop = ref(false);
const emits = defineEmits(["close"]);

const props = defineProps({
  prizeCodeList: {
    type: Array,
    default: [],
  },
});
console.log(props.prizeCodeList, "prizeCodeList========");
const closeClick = () => {
  emits("close", null);
};
// 查看我的中奖码
const myAllPrizeCodeClick = () => {
  showPrizeCodeAllPop.value = true;
};
const initData = () => {
  if (!isPreview) {
  }
};
initData();
</script>
<style lang="scss" scoped>
.getPrizeCodeDivAll {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/356751/17/3840/24969/690af514Fe13dfa07/8fb579bf733f0789.png);
  background-size: 100% 100%;
  width: 6.5rem;
  height: 7.88rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 2.2rem;
  .closeDiv {
    position: absolute;
    right: 0rem;
    top: 0rem;
    width: 0.8rem;
    height: 0.8rem;
    // background-color: red;
  }
  .prizeListDiv {
    height: 2.6rem;
    overflow-y: scroll;
    overflow-x: hidden;
    .prizeItemDiv {
      background-color: #fbf6ed;
      border-radius: 0.15rem;
      width: 5.9rem;
      height: 1.2rem;
      color: #000;
      font-size: 0.36rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-bottom: 0.15rem;
    }
  }
  .myAllPrizeCode {
    position: absolute;
    bottom: 0.9rem;
    width: 4rem;
    height: 1rem;
    left: 50%;
    transform: translateX(-50%);
    // background-color: red;
  }
}
</style>
