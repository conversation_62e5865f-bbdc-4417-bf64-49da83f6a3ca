<template>
  <div class="addCartSkuDivAll">
    <div class="closeDiv" @click="closeClick()"></div>
    <div class="skuListDiv">
      <div class="skuItemDiv" v-for="(item, index) in skuListData" :key="index">
        <div class="skuMainPictureDiv">
          <img :src="item.skuMainPicture" alt=""></img>
        </div>
        <div class="skuNameDiv">{{item.skuName}}</div>

      </div>
    </div>
    <div class="cartAllDivAll">
      <div class="cartAllDiv" @click="cartAllSkuList()">一键加购所有商品</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { isPreview } from "@/utils";
import { gotoSkuPage } from "@/utils/platforms/jump";
import { httpRequest } from "@/utils/service";
import dayjs from "dayjs";
import { closeToast, showLoadingToast, showToast } from "vant";
import { inject, onMounted, onUnmounted, ref } from "vue";
import { BaseInfo } from '@/types/BaseInfo';

const props = defineProps(['itemTaskData']);
const baseInfo = inject('baseInfo') as BaseInfo;
const skuListData = ref<any[]>([]);
const emits = defineEmits(["close","addCartSuccessEmit"]);
const closeClick = () => {
  emits("close", null);
};
const getSkuListData = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/getTaskAddSku");
    console.log(res, "加购商品列表==========");
    closeToast();
    skuListData.value = res.data;
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
// 一键加购所有商品
const cartAllSkuList = async () => {
  if (!isPreview) {
    console.log('一键加购所有商品');
    try {
      showLoadingToast({
        message: "加载中...",
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post("/96012/addSku",{
        skuId: 0
      });
      closeToast();
      emits("addCartSuccessEmit", res.data);
    } catch (error: any) {
      closeToast();
      console.error(error);
    }
  } else {
    showToast('活动预览，仅供查看');
  }
};
const initData = () => {
  if (!isPreview) {
    getSkuListData();
  } else {
    skuListData.value = props.itemTaskData.skuList;
  }
};
 initData();
</script>
<style lang="scss" scoped>
.addCartSkuDivAll {
  background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/348398/23/22357/8223/690aac62F8f53e374/0cf55b2e855420a4.png);
  background-size: cover;
  width: 7.5rem;
  height: 9.08rem;
  background-repeat: no-repeat;
  position: relative;
  padding-top: 1.42rem;
  .closeDiv {
    position: absolute;
    right: 0.38rem;
    top: 0.44rem;
    width: 0.3rem;
    height: 0.3rem;
    // background-color: red;
  }
  .skuListDiv {
    height: 6.0rem;
    overflow-y: scroll;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 6.9rem;
    margin-left: 50%;
    transform: translateX(-50%);
    // background-color: red;
    .skuItemDiv {
      width: 2.2rem;
      border-radius: 0.15rem;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      height: 3.0rem;
      position: relative;
      margin-bottom: 0.15rem;
      .skuMainPictureDiv{
        // width: 2.0rem;
        height: 2.0rem;
        margin-top: 0.02rem;
        width: 100%;
        display: flex;
        justify-content: center;
        // border: 0.02rem solid #000;
        box-sizing: border-box;
        img{
          width: 2rem;
          height: 2rem;
          object-fit: contain;
        }
      }
      .skuNameDiv{
        margin: 0.1rem 0.2rem 0 0.2rem;
        font-size: 0.24rem;
        color: #000;
        display: -webkit-box;        /* 必须结合 -webkit-box 布局 */
        -webkit-box-orient: vertical; /* 设置垂直方向排列 */
        -webkit-line-clamp: 2;       /* 限制显示行数为2行 */
        overflow: hidden;           /* 隐藏超出内容 */
        text-overflow: ellipsis;    /* 显示省略号 */
        text-align: center;
      }
    }
  }
  .cartAllDiv{
    border-radius: 0.5rem;
    background-color: #000;
    color: #fff;
    font-size: 0.36rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4rem;
    height: 1rem;
    margin-top: 0.2rem;
    margin-left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
