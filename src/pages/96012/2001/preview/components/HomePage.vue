<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="kvBox">
      <!-- 当只有一张图片时，直接显示图片 -->
      <img v-if="furnish.kvList && furnish.kvList.length === 1"
           :src="furnish.kvList[0].image"
           alt=""
           @click="handleJump(furnish.kvList[0].jumpUrl)">

      <!-- 当有多张图片时，使用Swiper轮播 -->
      <div v-else-if="furnish.kvList && furnish.kvList.length > 1" class="swiper-container kv-swiper">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in furnish.kvList" :key="index">
            <img :src="item.image" alt="" @click="handleJump(item.jumpUrl)">
          </div>
        </div>
      </div>

      <!-- 当没有图片时，显示默认内容 -->
      <div v-else class="no-image">暂无图片</div>
      <div class="btnBox">
        <div class="rightBtn" @click="rulePopup = true" :style="furnishStyles.rightBtnImgBg.value">活动规则</div>
        <div class="rightBtn" @click="prizeCodePopup = true" :style="furnishStyles.rightBtnImgBg.value">我的中奖码</div>
        <div class="rightBtn" @click="myPrizePopup = true" :style="furnishStyles.rightBtnImgBg.value">我的奖品</div>
      </div>
    </div>
    <div class="mainBox" :style="furnishStyles.pageBg.value">
      <!-- 影像互动 赢惊喜好礼-->
      <div class="mainPrizeArea" :style="furnishStyles.mainPrizeAreaBgColor.value">
        <img class="mainPrizeTitle" :src="furnish.mainPrizeTitle" alt="">
        <div class="mainPrizeBox" ref="prizeBoxRef">
          <div class="prizeItem" v-for="(item, index) in filteredLotteryPrizeList" :key="index">
            <div class="prizeImgBg" :style="furnishStyles.mainPrizeItemBgColor.value">
              <img :src="item.prizeImg" alt="">
            </div>
            <div class="prizeNameBox">{{item.prizeName}}</div>
          </div>
        </div>
        <div class="mainBtnBox">
          <img @click="showTaskListPop = true" :src="furnish.toTaskBtnBg" alt="">
          <img @click="showLotteryDrawDetailsPop = true" :src="furnish.toLotteryDetailsBtnBg" alt="">
        </div>
      </div>
      <!--2亿人像超清晰 旅拍何必带相机-->
      <img class="showImgAreaTitle" :src="furnish.showImgAreaTitle" alt="">
      <!--带tab的分区-->
      <div class="mainTabArea">
        <div
          class="mainTabWrapper"
          v-for="(tab, index) in sectionInfo"
          :key="index"
        >
          <div
            class="mainTab"
            :style="index === activeMainTab ? furnishStyles.mainTitleActive.value : furnishStyles.mainTitle.value"
            @click="switchMainTab(index)"
          >
            {{tab.title}}
          </div>
          <!-- 选中指示器 -->
          <div
            class="tabIndicator"
            v-if="index === activeMainTab"
            :style="{ backgroundColor: furnishStyles.mainTitleActive.value.color }"
          ></div>
        </div>
      </div>

      <!-- 子tab区域 -->
      <div class="secondTabBox">
        <div class="secondTabArea" v-if="currentChildTabs.length > 0">
          <!-- 全部选项 -->
          <div
            class="secondTab allTab"
            :style="activeSecondTab === -1 ? furnishStyles.secondaryTitleActive.value : furnishStyles.secondaryTitle.value"
            @click="switchSecondTab(-1)"
          >
            全部
          </div>
          <div
            class="secondTab"
            v-for="(child, childIndex) in currentChildTabs"
            :key="childIndex"
            :style="childIndex === activeSecondTab ? furnishStyles.secondaryTitleActive.value : furnishStyles.secondaryTitle.value"
            @click="switchSecondTab(childIndex)"
          >
            {{child.childTitle}}
          </div>
        </div>
        <div class="rightTab">
          <span :class="sortWay === 0 ? 'active' : ''" @click="sortWay = 0">最新</span>
          <span>|</span>
          <span :class="sortWay === 1 ? 'active' : ''" @click="sortWay = 1">最热</span>
        </div>
      </div>

      <!--展示vivo照片区域-->
      <div class="showImgArea">
         <ImagesList :imgList="imgList" :showUserInfo="true" :showRank="false" :showChangeShare="false"/>
      </div>
    </div>
  </div>
  <!--活动规则弹窗-->
  <VanPopup teleport="body" v-model:show="rulePopup" position="center">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <!--我的中奖码弹窗-->
  <VanPopup teleport="body" v-model:show="prizeCodePopup" position="center">
    <PrizeCode @close="prizeCodePopup = false"></PrizeCode>
  </VanPopup>
  <!--我的奖品弹窗-->
  <VanPopup teleport="body" v-model:show="myPrizePopup" position="center">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
  <!-- 做任务列表弹窗 -->
  <VanPopup teleport="body" v-model:show="showTaskListPop" position="bottom">
    <TaskList :taskListData="taskList" v-if="showTaskListPop" @close="closeTaskPop"></TaskList>
  </VanPopup>
  <VanPopup teleport="body" v-model:show="showLotteryDrawDetailsPop" position="bottom">
    <LotteryDrawDetailsPop
      v-if="showLotteryDrawDetailsPop"
      @close="showLotteryDrawDetailsPop = false"
    ></LotteryDrawDetailsPop>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, onUnmounted, watch, defineProps } from 'vue';
import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper';
import 'swiper/swiper.min.css';

import ImagesList from "../../components/ImagesList.vue";
import furnishStyles, { furnish } from '../../ts/furnishStyles';
import Rule from "../../components/Rule.vue";
import PrizeCode from "../../components/PrizeCode.vue";
import MyPrize from "../../components/MyPrize.vue";
import LotteryDrawDetailsPop from "../../components/LotteryDrawDetails.vue";
import TaskList from "../../components/TaskList.vue";

const rulePopup = ref(false);
const prizeCodePopup = ref(false);
const myPrizePopup = ref(false);
const showTaskListPop = ref(false); // 做任务列表弹窗
const showLotteryDrawDetailsPop = ref(false);

// Swiper 实例
let swiperInstance: Swiper | null = null;

// 奖品滚动相关
const prizeBoxRef = ref<HTMLElement | null>(null);
let animationFrameId: number | null = null;

const props = defineProps(['periodList', 'sectionInfo', 'ruleText', 'taskList']);
console.log('props', props)
// 添加tab选中状态
const activeMainTab = ref(0);
const activeSecondTab = ref(0);

// 计算当前主tab下的子tab列表
const currentChildTabs = computed(() => {
  if (props.sectionInfo && props.sectionInfo.length > 0) {
    return props.sectionInfo[activeMainTab.value]?.childSectionList || [];
  }
  return [];
});

// 过滤掉下架的奖品（status === 0）
const filteredLotteryPrizeList = computed(() => {
  const prizeList = props.periodList?.[0]?.lotteryPrizeList || [];
  return prizeList.filter(item => item.status !== 0);
});
// 排序方式 0-最新 1-最热
const sortWay = ref(0);
const imgList = ref([
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    title: '标题1',
    content: '描述1',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户1',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题2',
    content: '描述2',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户2',
    fans: 10009,
    createTime: '2025-11-20',
    liked: false,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/291461/39/27351/11615/69046e4cF2e58da62/e0ba47c13aa9d89d.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题3',
    content: '描述3',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户3',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题4',
    content: '描述4',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户4',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题5',
    content: '描述5',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户5',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题6',
    content: '描述6',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户6',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
  },
]);

// 初始化 Swiper
const initSwiper = () => {
  nextTick(() => {
    const swiperElement = document.querySelector('.kv-swiper');
    if (swiperElement && furnish.kvList && furnish.kvList.length > 1) {
      // 销毁旧实例
      if (swiperInstance) {
        swiperInstance.destroy(true, true);
      }
      // 创建新实例
      swiperInstance = new Swiper(swiperElement as HTMLElement, {
        modules: [Autoplay, Pagination],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
      });
    }
  });
};

// 销毁 Swiper 实例
const destroySwiper = () => {
  if (swiperInstance) {
    swiperInstance.destroy(true, true);
    swiperInstance = null;
  }
};

// 开始奖品自动滚动
const startPrizeScroll = () => {
  // 停止之前的滚动
  stopPrizeScroll();

  if (!prizeBoxRef.value || !filteredLotteryPrizeList.value || filteredLotteryPrizeList.value.length <= 3) return;

  const prizeBox = prizeBoxRef.value;
  let scrollPosition = 0;
  const scrollSpeed = 1; // 滚动速度

  // 先复制奖品项以实现无缝滚动效果
  const clonePrizeItems = () => {
    // 清除之前克隆的元素
    const clonedItems = prizeBox.querySelectorAll('.prizeItem[data-cloned]');
    clonedItems.forEach(item => item.remove());

    // 克隆所有奖品项
    const prizeItems = prizeBox.querySelectorAll('.prizeItem:not([data-cloned])');
    prizeItems.forEach(item => {
      const clone = item.cloneNode(true) as HTMLElement;
      clone.setAttribute('data-cloned', 'true');
      prizeBox.appendChild(clone);
    });
  };

  const scroll = () => {
    scrollPosition += scrollSpeed;
    if (scrollPosition >= prizeBox.scrollWidth / 2) {
      scrollPosition = 0;
      prizeBox.scrollLeft = 0; // 重置滚动位置以确保平滑过渡
    }
    prizeBox.scrollLeft = scrollPosition;
    animationFrameId = requestAnimationFrame(scroll);
  };

  // 等待DOM更新后执行
  nextTick(() => {
    clonePrizeItems();
    animationFrameId = requestAnimationFrame(scroll);
  });
};

// 停止奖品滚动
const stopPrizeScroll = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

const handleJump = (jumpUrl: string) => {
  if (jumpUrl) {
    window.location.href = jumpUrl;
  }
};

// 主tab切换
const switchMainTab = (index: number) => {
  activeMainTab.value = index;
  // 重置子tab选中为第一个
  activeSecondTab.value = 0;
};

// 子tab切换
const switchSecondTab = (index: number) => {
  activeSecondTab.value = index;
};

// 检查是否选择了"全部"
const isAllSelected = computed(() => {
  return activeSecondTab.value === -1;
});

// 关闭任务弹窗
const closeTaskPop = (data: string) => {
  showTaskListPop.value = false;
  console.log(data, "任务回调========");
  // 在preview模式下，不需要处理具体的任务回调逻辑
};

// 监听奖品列表变化，重新启动滚动
watch(filteredLotteryPrizeList, () => {
  nextTick(() => {
    startPrizeScroll();
  });
});

// 组件挂载后初始化 Swiper
onMounted(() => {
  // 使用延迟初始化，确保 DOM 更新完成
  setTimeout(() => {
    initSwiper();
    startPrizeScroll(); // 启动奖品滚动
  }, 200);
});

// 组件卸载前清理定时器
onUnmounted(() => {
  stopPrizeScroll();
});
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.kvBox {
  width: 7.5rem;
  position: relative;

  img {
    width: 100%;
    display: block;
  }

  .no-image {
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }

  .btnBox{
    position: absolute;
    right: 0.3rem;
    top: 0.46rem;
    z-index: 10;
    .rightBtn{
      width: 1.3rem;
      height: 0.5rem;
      line-height: 0.5rem;
      font-size: 0.22rem;
      text-align: center;
      background-size: 100%;
      background-repeat: no-repeat;
      margin-bottom: 0.2rem;
      cursor: pointer;
    }
  }
}

.kv-swiper {
  width: 7.5rem;
  height: 100%;

  .swiper-slide {
    img {
      width: 100%;
      display: block;
    }
  }

  .swiper-pagination {
    bottom: 10px;

    :deep(.swiper-pagination-bullet) {
      width: 8px;
      height: 8px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 1;
    }

    :deep(.swiper-pagination-bullet-active) {
      background: #fff;
    }
  }
}

.mainBox{
  z-index: 10;
  width: 7.5rem;
  border-radius: 0.5rem 0.5rem 0 0;
  position: relative;
  top: -0.5rem;
  padding: 0.82rem 0 0 0;

  .mainPrizeArea{
    width: 6.9rem;
    height: 5.5rem;
    border-radius: 0.16rem;
    margin: 0 auto 0.62rem;
    padding: 0.62rem 0 0 0;
    .mainPrizeTitle{
      height: 0.68rem;
      margin: 0 auto;
    }
    .mainPrizeBox{
      display: flex;
      width: 6.3rem;
      min-height: 3.3rem;
      /* justify-content: space-between; */
      margin: 0 auto;
      padding: 0.26rem 0 0 0;
      overflow: hidden;
      overflow-x: hidden; // 隐藏滚动条
      position: relative;
      .prizeItem{
        width: 2rem;
        height: 2.8rem;
        text-align: center;
        margin-right: 0.15rem;
        font-size: 0.22rem;
        flex-shrink: 0; // 防止压缩
        .prizeImgBg{
          width: 2rem;
          height: 2rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.08rem;
          img{
            width: 1.5rem;
            height: 1.5rem;
            margin: 0 auto;
          }
        }
        .prizeNameBox{
          width: 2rem;
          height: 0.7rem;
          margin: 0.2rem auto;
          overflow: hidden;
          word-break: break-all;
          text-align: center;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
    }
    .mainBtnBox{
      display: flex;
      justify-content: space-between;
      width: 6.3rem;
      height: 0.6rem;
      margin: 0 auto 0.4rem;
      img{
        width: 3.08rem;
        height: 0.6rem;
      }
    }
  }
  .showImgAreaTitle{
    height: 0.33rem;
    margin: 0.62rem auto 0.88rem;
  }
  .mainTabArea{
    width: 6.9rem;
    height: 1rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    overflow-x: scroll;
    &::-webkit-scrollbar {
      display: none;
    }

    .mainTabWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 auto;

      .mainTab {
        //width: 3.45rem;
        height: 0.6rem;
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.3rem;
        font-weight: bold;
        cursor: pointer;
      }

      .tabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
      }
    }
  }
  .secondTabBox {
    width: 7.5rem;
    display: flex;
    padding: 0 0.2rem;

    .secondTabArea {
      width: 6.3rem;
      margin: 0.2rem auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      overflow-x: auto;
      box-sizing: border-box;

      &::-webkit-scrollbar {
        display: none;
      }

      .secondTab {
        flex-shrink: 0;
        height: 0.5rem;
        line-height: 0.5rem;
        font-size: 0.22rem;
        padding: 0 0.2rem;
        margin-right: 0.2rem;
        border-radius: 0.25rem;
        cursor: pointer;
        text-align: center;

        &.allTab {
          background-color: #f5f5f5;
          color: #333;
        }
      }
    }

    .rightTab {
      width: 1.6rem;
      font-size: 0.24rem;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: right;

      .active {
        color: #415fff;
        cursor: pointer;
      }

      span {
        margin: 0 0.05rem;
        cursor: pointer;
      }
    }
  }
  .showImgArea{
    width: 6.9rem;
    margin: 0 auto;
    padding: 0 0 0.73rem;
  }
}</style>
