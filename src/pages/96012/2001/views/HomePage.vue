<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="kvBox">
      <!-- 当只有一张图片时，直接显示图片 -->
      <img
        v-if="furnish.kvList && furnish.kvList.length === 1"
        :src="furnish.kvList[0].image"
        alt=""
        @click="handleJump(furnish.kvList[0].jumpUrl)"
      />

      <!-- 当有多张图片时，使用Swiper轮播 -->
      <div
        v-else-if="furnish.kvList && furnish.kvList.length > 1"
        class="swiper-container kv-swiper"
      >
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in furnish.kvList" :key="index">
            <img :src="item.image" alt="" @click="handleJump(item.jumpUrl)" />
          </div>
        </div>
      </div>

      <!-- 当没有图片时，显示默认内容 -->
      <div v-else class="no-image">暂无图片</div>
      <div class="btnBox">
        <div
          @click="rulePopup = true"
          v-click-track="'hdgz'"
          class="rightBtn"
          :style="furnishStyles.rightBtnImgBg.value"
        >
          活动规则
        </div>
        <div
          @click="prizeCodePopup = true"
          v-click-track="'wdzjm'"
          class="rightBtn"
          :style="furnishStyles.rightBtnImgBg.value"
        >
          我的中奖码
        </div>
        <div
          @click="myPrizePopup = true"
          v-click-track="'wdjp'"
          class="rightBtn"
          :style="furnishStyles.rightBtnImgBg.value"
        >
          我的奖品
        </div>
      </div>
    </div>
    <div class="mainBox" :style="furnishStyles.pageBg.value">
      <!-- 影像互动 赢惊喜好礼-->
      <div class="mainPrizeArea" :style="furnishStyles.mainPrizeAreaBgColor.value">
        <img class="mainPrizeTitle" :src="furnish.mainPrizeTitle" alt="" />
        <div class="mainPrizeBox" ref="prizeBoxRef">
          <div class="prizeItem" v-for="(item, index) in lotteryPrizeList" :key="index">
            <div class="prizeImgBg" :style="furnishStyles.mainPrizeItemBgColor.value">
              <img :src="item.prizeImg" alt="" />
            </div>
            <div class="prizeNameBox">{{ item.prizeName }}</div>
          </div>
        </div>
        <div class="mainBtnBox">
          <img :src="furnish.toTaskBtnBg" alt="" @click="doTaskClick()" />
          <img
            :src="furnish.toLotteryDetailsBtnBg"
            alt=""
            @click="lotteryDrawDetailsClick()"
          />
        </div>
      </div>
      <!--2亿人像超清晰 旅拍何必带相机-->
      <img class="showImgAreaTitle" :src="furnish.showImgAreaTitle" alt="" />
      <!--带tab的分区-->
      <div class="mainTabArea">
        <div class="mainTabWrapper" v-for="(tab, index) in sectionInfo" :key="index">
          <div
            class="mainTab"
            :style="
              index === activeMainTab
                ? furnishStyles.mainTitleActive.value
                : furnishStyles.mainTitle.value
            "
            @click="switchMainTab(index, tab.fatherSectionId)"
          >
            {{ tab.fatherSectionName }}
          </div>
          <!-- 选中指示器 -->
          <div
            class="tabIndicator"
            v-if="index === activeMainTab"
            :style="{ backgroundColor: furnishStyles.mainTitleActive.value.color }"
          ></div>
        </div>
      </div>

      <!-- 子tab区域 -->
      <div class="secondTabBox">
        <div class="secondTabArea" v-if="currentChildTabs.length > 0">
          <!-- 全部选项 -->
          <div
            class="secondTab allTab"
            :style="
              activeSecondTab === -1
                ? furnishStyles.secondaryTitleActive.value
                : furnishStyles.secondaryTitle.value
            "
            @click="switchSecondTab(-1, '-1')"
          >
            全部
          </div>
          <div
            class="secondTab"
            v-for="(child, childIndex) in currentChildTabs"
            :key="childIndex"
            :style="
              childIndex === activeSecondTab
                ? furnishStyles.secondaryTitleActive.value
                : furnishStyles.secondaryTitle.value
            "
            @click="switchSecondTab(childIndex, child.childSectionId)"
          >
            {{ child.childSectionName }}
          </div>
        </div>
        <div class="rightTab">
          <span
            :class="sortWay === 0 ? 'active' : ''"
            @click="(sortWay = 0), resetPagination(), getDynamicBySection()"
            >最新</span
          >
          <span>|</span>
          <span
            :class="sortWay === 1 ? 'active' : ''"
            @click="(sortWay = 1), resetPagination(), getDynamicBySection()"
            >最热</span
          >
        </div>
      </div>

      <!--展示vivo照片区域-->
      <div class="showImgArea">
        <ImagesList
          :imgList="imgsList"
          :showUserInfo="true"
          :showRank="false"
          :showChangeShare="false"
          :hasMoreData="hasMoreData"
          :isLoadingMore="isLoadingMore"
          @likeThisPost="likeThisPostCallBack"
          @closeDetailPop="likeThisPostCallBack"
          @loadMore="handleLoadMore"
        />
      </div>
    </div>
  </div>
  <!--活动规则弹窗-->
  <VanPopup teleport="body" v-model:show="rulePopup" position="center">
    <Rule @close="rulePopup = false" :rule="ruleText"></Rule>
  </VanPopup>
  <!--我的中奖码弹窗-->
  <VanPopup teleport="body" v-model:show="prizeCodePopup" position="center">
    <PrizeCode @close="prizeCodePopup = false"></PrizeCode>
  </VanPopup>
  <!--我的奖品弹窗-->
  <VanPopup teleport="body" v-model:show="myPrizePopup" position="center">
    <MyPrize @close="myPrizePopup = false"></MyPrize>
  </VanPopup>
  <!-- 做任务列表弹窗 -->
  <VanPopup teleport="body" v-model:show="showTaskListPop" position="bottom">
    <TaskList :taskListData="[]" v-if="showTaskListPop" @close="closeTaskPop"></TaskList>
  </VanPopup>
  <!-- 开奖明细 -->
  <VanPopup teleport="body" v-model:show="showLotteryDrawDetailsPop" position="bottom">
    <LotteryDrawDetailsPop
      v-if="showLotteryDrawDetailsPop"
      @close="showLotteryDrawDetailsPop = false"
    ></LotteryDrawDetailsPop>
  </VanPopup>
  <!-- 中奖结果 -->
  <VanPopup teleport="body" v-model:show="showDrawResultPop" position="center">
    <DrawResultPop
      v-if="showDrawResultPop"
      :drawResultData="drawResultData"
      @saveAddress="saveAddressEmit"
      @close="showDrawResultPop = false"
    ></DrawResultPop>
  </VanPopup>
  <!-- 填写收货地址 -->
  <VanPopup teleport="body" v-model:show="showAddressPop" position="center">
    <AddressPop
      v-if="showAddressPop"
      :addressId="addressId"
      :activityPrizeId="activityPrizeId"
      :prizeRecordId="prizeRecordId"
      :writeAddress="writeAddress"
      @close="showAddressPop = false"
    ></AddressPop>
  </VanPopup>
  <!-- 通过别人的分享进入的活动 -->
  <VanPopup teleport="body" v-model:show="imgDetailPopup" position="center">
    <ShowImagesDetail
      v-if="imgDetailPopup"
      :imgDetail="imgDetail"
      @closePop="imgDetailPopup = false"
      :showUserInfo="true"
    />
  </VanPopup>
  <!-- 获得中奖码弹窗 -->
  <VanPopup teleport="body" v-model:show="showGetPrizeCodePop" position="center">
    <GetPrizeCodePop
      v-if="showGetPrizeCodePop"
      :prizeCodeList="prizeCode"
      @close="showGetPrizeCodePop = false"
    ></GetPrizeCodePop>
  </VanPopup>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  nextTick,
  defineProps,
  computed,
  inject,
  onUnmounted,
  watch,
} from "vue";
import { closeToast, showLoadingToast, showToast } from "vant";
import Swiper from "swiper";
import "swiper/swiper.min.css";
import { Autoplay, Pagination } from "swiper";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { httpRequest } from "@/utils/service";
import ImagesList from "../components/ImagesList.vue";
import Rule from "../components/Rule.vue";
import MyPrize from "../components/MyPrize.vue";
import PrizeCode from "../components/PrizeCode.vue";
import LotteryDrawDetailsPop from "../components/LotteryDrawDetails.vue";
import { checkActTime } from "../ts/logic";
import { BaseInfo } from "@/types/BaseInfo";
import TaskList from "../components/TaskList.vue";
import DrawResultPop from "../components/DrawResult.vue";
import AddressPop from "../components/SaveAddress.vue";
import constant from "@/utils/constant";
import GetPrizeCodePop from "../components/GetPrizeCode.vue";

const showAddressPop = ref(false);
const emits = defineEmits(["operType"]);
const showLotteryDrawDetailsPop = ref(false);
const showGetPrizeCodePop = ref(false);
const prizeCode = ref<any[]>([]);
const imgDetailPopup = ref(false);
const showDrawResultPop = ref(false);
const drawResultData = ref<any>({});
const addressId = ref("");
const activityPrizeId = ref("");
const prizeRecordId = ref("");
const writeAddress = ref(false);
// 中奖弹窗填写地址回调
const saveAddressEmit = (datas: any) => {
  addressId.value = datas.addressId;
  activityPrizeId.value = datas.activityPrizeId;
  prizeRecordId.value = datas.prizeRecordId;
  writeAddress.value = datas.writeAddress;
  showAddressPop.value = true;
  showDrawResultPop.value = false;
};
const imgDetail = ref<any>(null); // 发布信息
const baseInfo = inject("baseInfo") as BaseInfo;
const pathParams = inject("pathParams") as any;
// Swiper 实例
let swiperInstance: Swiper | null = null;
const showTaskListPop = ref(false); // 做任务列表弹窗
// 添加tab选中状态
const activeMainTab = ref(0);
const activeSecondTab = ref(-1);
const joinPopup = ref(false);

// 分区信息数据
const sectionInfo = ref<any[]>([]);

const ruleText = ref("");
const rulePopup = ref(false);
const prizeCodePopup = ref(false);
const myPrizePopup = ref(false);

// 奖品滚动相关
const prizeBoxRef = ref<HTMLElement | null>(null);
let animationFrameId: number | null = null;

// 展示活动规则，首次获取规则
const getRuleText = async () => {
  try {
    if (!ruleText.value) {
      const { data } = await httpRequest.get("/common/getRule");
      ruleText.value = data;
    }
  } catch (error: any) {
    console.error();
  }
};

interface PrizeItem {
  prizeImg: string;
  prizeName: string;
  // 可以根据实际接口返回添加更多字段
}

const lotteryPrizeList = ref<PrizeItem[]>([]);
// 获取奖品信息
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post("/96012/getPrizes", {
      type: 1, //类型 1-抽奖奖品 2-排行奖品
    });
    lotteryPrizeList.value = data;
    console.log("data", data);
  } catch (error: any) {
    console.error();
  }
};

// 获取主分区以及分区tab列表
const getSectionInfo = async () => {
  try {
    const { data } = await httpRequest.post("/96012/getSectionInfo");
    sectionInfo.value = data;
  } catch (error) {
    console.error(error);
    console.log("获取分区信息失败");
  }
};

// 计算当前主tab下的子tab列表
const currentChildTabs = computed(() => {
  if (sectionInfo.value && sectionInfo.value.length > 0) {
    return sectionInfo.value[activeMainTab.value]?.childSectionList || [];
  }
  return [];
});

const imgsList = ref([]);

// 初始化 Swiper
const initSwiper = () => {
  nextTick(() => {
    const swiperElement = document.querySelector(".kv-swiper");
    if (swiperElement && furnish.kvList && furnish.kvList.length > 1) {
      // 销毁旧实例
      if (swiperInstance) {
        swiperInstance.destroy(true, true);
      }
      // 创建新实例
      swiperInstance = new Swiper(swiperElement as HTMLElement, {
        modules: [Autoplay, Pagination],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false,
        },
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      });
    }
  });
};

// 销毁 Swiper 实例
const destroySwiper = () => {
  if (swiperInstance) {
    swiperInstance.destroy(true, true);
    swiperInstance = null;
  }
};

// 开始奖品自动滚动
const startPrizeScroll = () => {
  // 停止之前的滚动
  stopPrizeScroll();

  if (!prizeBoxRef.value || lotteryPrizeList.value.length <= 3) return;

  const prizeBox = prizeBoxRef.value;
  let scrollPosition = 0;
  const scrollSpeed = 1; // 滚动速度

  // 先复制奖品项以实现无缝滚动效果
  const clonePrizeItems = () => {
    // 清除之前克隆的元素
    const clonedItems = prizeBox.querySelectorAll(".prizeItem[data-cloned]");
    clonedItems.forEach((item) => item.remove());

    // 克隆所有奖品项
    const prizeItems = prizeBox.querySelectorAll(".prizeItem:not([data-cloned])");
    prizeItems.forEach((item) => {
      const clone = item.cloneNode(true) as HTMLElement;
      clone.setAttribute("data-cloned", "true");
      prizeBox.appendChild(clone);
    });
  };

  const scroll = () => {
    scrollPosition += scrollSpeed;
    if (scrollPosition >= prizeBox.scrollWidth / 2) {
      scrollPosition = 0;
      prizeBox.scrollLeft = 0; // 重置滚动位置以确保平滑过渡
    }
    prizeBox.scrollLeft = scrollPosition;
    animationFrameId = requestAnimationFrame(scroll);
  };

  // 等待DOM更新后执行
  nextTick(() => {
    clonePrizeItems();
    animationFrameId = requestAnimationFrame(scroll);
  });
};

// 停止奖品滚动
const stopPrizeScroll = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

const handleJump = (jumpUrl: string) => {
  if (jumpUrl) {
    window.location.href = jumpUrl;
  }
};

// 排序方式 0-最新 1-最热
const sortWay = ref(0);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(20);
const hasMoreData = ref(true);
const isLoadingMore = ref(false);

const activeFatherSectionId = ref("");
// 主tab切换
const switchMainTab = (index: number, fatherSectionId: string) => {
  activeMainTab.value = index;
  activeFatherSectionId.value = fatherSectionId;
  // 重置子tab选中为第一个
  activeSecondTab.value = -1;
  activeChildSectionId.value = "-1";
  // 重置分页状态
  resetPagination();
  getDynamicBySection();
};

const activeChildSectionId = ref("-1");
// 子tab切换
const switchSecondTab = (index: number, childSectionId: string) => {
  activeSecondTab.value = index;
  activeChildSectionId.value = childSectionId;
  // 重置分页状态
  resetPagination();
  getDynamicBySection();
};

// 重置分页状态
const resetPagination = () => {
  currentPage.value = 1;
  hasMoreData.value = true;
  isLoadingMore.value = false;
};

// 根据分类详情获取用户动态信息
const getDynamicBySection = async (isLoadMore = false) => {
  try {
    // 如果是加载更多，设置加载状态
    if (isLoadMore) {
      isLoadingMore.value = true;
    }

    const { data } = await httpRequest.post("/96012/getDynamicBySection", {
      childSectionId: activeChildSectionId.value || "-1",
      fatherSectionId:
        activeFatherSectionId.value || sectionInfo.value[0]?.fatherSectionId,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      queryWay: 0, //查询方式 0-所有动态 1-我的动态 2-我点赞的动态
      sortWay: sortWay.value,
    });

    console.log(data.records);

    if (isLoadMore) {
      // 加载更多时追加数据
      imgsList.value = [...imgsList.value, ...data.records];
    } else {
      // 首次加载或切换tab时替换数据
      imgsList.value = data.records;
    }

    // 检查是否还有更多数据
    hasMoreData.value = data.records.length === pageSize.value;
  } catch (error) {
    console.error(error);
    console.log("获取动态信息失败");
  } finally {
    if (isLoadMore) {
      isLoadingMore.value = false;
    }
  }
};

// 处理加载更多
const handleLoadMore = () => {
  if (hasMoreData.value && !isLoadingMore.value) {
    currentPage.value++;
    getDynamicBySection(true);
  }
};

/**
 * 点赞的回调
 * @param item
 */
const likeThisPostCallBack = (item: any) => {
  console.log("点击了点赞");
  // 点赞后重新获取当前页数据，但不重置分页
  const tempPage = currentPage.value;
  currentPage.value = 1;
  getDynamicBySection().then(() => {
    currentPage.value = tempPage;
  });
};

// 检查是否选择了"全部"
const isAllSelected = computed(() => {
  return activeSecondTab.value === -1;
});

// 点击做任务 获得中奖码按钮
const doTaskClick = () => {
  console.log("点击做任务 获得中奖码按钮");
  showTaskListPop.value = true;
};
// 查看中奖码开奖明细
const lotteryDrawDetailsClick = () => {
  showLotteryDrawDetailsPop.value = true;
};
const closeTaskPop = (data: string) => {
  showTaskListPop.value = false;
  console.log(data, "任务回调========");
  if (data && data === "taskUpload") {
    emits("operType", "taskUpload");
  } else if (data && data === "taskInviteShare") {
    emits("operType", "taskInviteShare");
  }
};

// 监听奖品列表变化，重新启动滚动
watch(lotteryPrizeList, () => {
  nextTick(() => {
    startPrizeScroll();
  });
});

// 组件挂载后初始化 Swiper
onMounted(() => {
  // 使用延迟初始化，确保 DOM 更新完成
  setTimeout(() => {
    initSwiper();
    startPrizeScroll(); // 启动奖品滚动
  }, 200);
});

// 组件卸载前清理定时器
onUnmounted(() => {
  stopPrizeScroll();
});
const shareConfig = JSON.parse(
  window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? ""
);
// 查询分享者分享的图片的信息
const getShareAllData = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/getDynamicById", {
      dynamicId: pathParams.dynamicId,
    });
    imgDetail.value = "";
    imgDetailPopup.value = true;
    closeToast();
  } catch (error: any) {
    closeToast();
    console.error(error, "查询分享者分享的图片的信息失败=========");
  }
};
// 购买商品以及入会任务中奖码
const doTaskMemberAndOrder = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post("/96012/doTaskMemberAndOrder");
    if (res.data && res.data.length > 0) {
      prizeCode.value = res.data;
      showGetPrizeCodePop.value = true;
    }
    closeToast();
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};
const isLoadingFinish = ref(false);
const initHomePage = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([
      getRuleText(),
      getPrizes(),
      getSectionInfo(),
      getDynamicBySection(),
      doTaskMemberAndOrder(),
    ]);
    console.log(pathParams, shareConfig, "pathParams========");
    if (pathParams.shareId && pathParams.shareId !== shareConfig.shareId) {
      // 通过分享进入且进入的人与分享的人不是同一个人 调用接口查询分享者分享的图片信息
      getShareAllData();
    }
    if (pathParams.shareId && pathParams.shareId === shareConfig.shareId) {
      showToast({
        message: "不能给自己点赞",
        duration: 2000,
        forbidClick: true,
        onClose: () => {},
      });
    }
    isLoadingFinish.value = true;
    closeToast();
    if (!checkActTime(baseInfo)) {
      return;
    }
    if (
      baseInfo.thresholdResponseList.length &&
      baseInfo.thresholdResponseList[0].thresholdCode === 4
    ) {
      joinPopup.value = true;
      console.log("您不是会员");
      return;
    }
  } catch (error: any) {
    console.error(error);

    closeToast();
  }
};
initHomePage();
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.kvBox {
  width: 7.5rem;
  position: relative;

  img {
    width: 100%;
    display: block;
  }

  .no-image {
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }

  .btnBox {
    position: absolute;
    right: 0.3rem;
    top: 0.46rem;
    z-index: 10;
    .rightBtn {
      width: 1.3rem;
      height: 0.5rem;
      line-height: 0.5rem;
      font-size: 0.22rem;
      text-align: center;
      background-size: 100%;
      background-repeat: no-repeat;
      margin-bottom: 0.2rem;
    }
  }
}

.kv-swiper {
  width: 7.5rem;
  height: 100%;

  .swiper-slide {
    img {
      width: 100%;
      display: block;
    }
  }

  .swiper-pagination {
    bottom: 10px;

    :deep(.swiper-pagination-bullet) {
      width: 8px;
      height: 8px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 1;
    }

    :deep(.swiper-pagination-bullet-active) {
      background: #fff;
    }
  }
}

.mainBox {
  z-index: 10;
  width: 7.5rem;
  border-radius: 0.5rem 0.5rem 0 0;
  position: relative;
  top: -0.5rem;
  padding: 0.82rem 0 0 0;

  .mainPrizeArea {
    width: 6.9rem;
    height: 5.5rem;
    border-radius: 0.16rem;
    margin: 0 auto 0.62rem;
    padding: 0.62rem 0 0 0;
    .mainPrizeTitle {
      height: 0.68rem;
      margin: 0 auto;
    }
    .mainPrizeBox {
      display: flex;
      width: 6.3rem;
      min-height: 3.3rem;
      /* justify-content: space-between; */
      margin: 0 auto;
      padding: 0.26rem 0 0 0;
      overflow: hidden;
      overflow-x: hidden; // 隐藏滚动条
      position: relative;
      .prizeItem {
        width: 2rem;
        height: 2.8rem;
        text-align: center;
        margin-right: 0.15rem;
        font-size: 0.22rem;
        flex-shrink: 0; // 防止压缩
        .prizeImgBg {
          width: 2rem;
          height: 2rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.08rem;
          img {
            width: 1.5rem;
            height: 1.5rem;
            margin: 0 auto;
          }
        }
        .prizeNameBox {
          width: 2rem;
          height: 0.7rem;
          margin: 0.2rem auto;
          overflow: hidden;
          word-break: break-all;
          text-align: center;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
    }
    .mainBtnBox {
      display: flex;
      justify-content: space-between;
      width: 6.3rem;
      height: 0.6rem;
      margin: 0 auto 0.4rem;
      img {
        width: 3.08rem;
        height: 0.6rem;
      }
    }
  }
  .showImgAreaTitle {
    height: 0.33rem;
    margin: 0.62rem auto 0.88rem;
  }
  .mainTabArea {
    width: 6.9rem;
    height: 1rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    overflow-x: scroll;
    &::-webkit-scrollbar {
      display: none;
    }

    .mainTabWrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 auto;

      .mainTab {
        //width: 3.45rem;
        height: 0.6rem;
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.3rem;
        font-weight: bold;
        cursor: pointer;
      }

      .tabIndicator {
        width: 0.53rem;
        height: 0.06rem;
        margin-top: 0.05rem;
        border-radius: 0.03rem;
      }
    }
  }

  .secondTabBox {
    width: 7.5rem;
    display: flex;
    padding: 0 0.2rem;
    .secondTabArea {
      width: 6.3rem;
      margin: 0.2rem auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      overflow-x: auto;
      box-sizing: border-box;

      &::-webkit-scrollbar {
        display: none;
      }

      .secondTab {
        flex-shrink: 0;
        height: 0.5rem;
        line-height: 0.5rem;
        font-size: 0.22rem;
        padding: 0 0.2rem;
        margin-right: 0.2rem;
        border-radius: 0.25rem;
        cursor: pointer;
        text-align: center;

        &.allTab {
          background-color: #f5f5f5;
          color: #333;
        }
      }
    }
    .rightTab {
      width: 1.6rem;
      font-size: 0.24rem;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: right;
      .active {
        color: #415fff;
        cursor: pointer;
      }
      span {
        margin: 0 0.05rem;
        cursor: pointer;
      }
    }
  }

  .showImgArea {
    width: 6.9rem;
    margin: 0 auto;
    padding: 0 0 0.73rem;
  }
}
</style>
